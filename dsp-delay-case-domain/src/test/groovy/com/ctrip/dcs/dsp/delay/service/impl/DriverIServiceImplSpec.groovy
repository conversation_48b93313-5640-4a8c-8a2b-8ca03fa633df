package com.ctrip.dcs.dsp.delay.service.impl


import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue
import com.ctrip.dcs.dsp.delay.filter.FilterChain
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DriverIServiceImplSpec extends Specification {

    def driverGateway = Mock(DriverGateway)

    def delayDspOrderRepository = Mock(DelayDspOrderRepository)

    def filterChain = Mock(FilterChain)

    def supplyOrderGateway = Mock(SupplyOrderGateway)

    def selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def grayscaleQConfig = Mock(GrayscaleQConfig)



    def driverServiceImpl = new DriverServiceImpl(driverGateway: driverGateway, delayDspOrderRepository: delayDspOrderRepository, filterChain: filterChain, supplyOrderGateway: supplyOrderGateway, selfDispatcherOrderGateway: selfDispatcherOrderGateway, delayDspCommonQConfig: delayDspCommonQConfig, grayscaleQConfig: grayscaleQConfig)

    @Unroll
    def "query queryDriverAggregationWithProfitFilter"() {
        given: "Mock数据"
        driverGateway.query(_, _,_) >> Lists.newArrayList(new Driver())
        filterChain.filter(_, _) >> driverList
        delayDspOrderRepository.queryByTaskId(_, _) >> queryByTaskId
        delayDspCommonQConfig.isReachStandardCitySwitch(_) >> reachStandardCitySwitch
        grayscaleQConfig.isGrayscaleCity(_) >> dispatcherOrderCitySwitch
        grayscaleQConfig.isGrayscaleCityAndCarTypeId(_, _) >> dispatcherOrderCitySwitch
        delayDspOrderRepository.query(_, _, _) >> Lists.newArrayList()
        supplyOrderGateway.queryByDriver(_, _, _, _, _) >> Lists.newArrayList()
        driverGateway.queryDriverScore(_) >> Lists.newArrayList()
        delayDspOrderRepository.queryByOrderIds(_) >> queryList
        when: "执行校验方法"
        def res = driverServiceImpl.queryDriverAggregationWithProfitFilter(delayDspTask, new DriverProfitDayBaselineValue(100, 200))

        then: "验证校验结果"
        res.size() == resultRes
        where:
        driverList                                    | reachStandardCitySwitch | dispatcherOrderCitySwitch | queryByTaskId                                                         | queryList                                                  | delayDspTask       || resultRes
        null                                          | null                    | null                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 0
        Lists.newArrayList(new Driver(driverId: "1")) | false                   | true                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | false                   | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new DelayDspOrder(driverOrderFee: 1000d)) | new DelayDspTask() || 0
    }


    @Unroll
    def "query queryPreOutPoolDrivers"() {
        given: "Mock数据"
        driverGateway.query(_, _,_) >> Lists.newArrayList(new Driver())
        filterChain.filter(_, _) >> driverList
        delayDspOrderRepository.queryByTaskId(_, _) >> queryByTaskId
        delayDspCommonQConfig.isReachStandardCitySwitch(_) >> reachStandardCitySwitch
        grayscaleQConfig.isGrayscaleCity(_) >> dispatcherOrderCitySwitch
        grayscaleQConfig.isGrayscaleCityAndCarTypeId(_, _) >> dispatcherOrderCitySwitch
        delayDspOrderRepository.query(_, _, _) >> Lists.newArrayList()
        supplyOrderGateway.queryByDriver(_, _, _, _, _) >> Lists.newArrayList()
        driverGateway.queryDriverScore(_) >> Lists.newArrayList()
        delayDspOrderRepository.queryByOrderIds(_) >> queryList
        when: "执行校验方法"
        def res = driverServiceImpl.queryPreOutPoolDrivers(delayDspTask, new DriverProfitDayBaselineValue(100, 200), Maps.newHashMap())

        then: "验证校验结果"
        res.size() == resultRes
        where:
        driverList                                    | reachStandardCitySwitch | dispatcherOrderCitySwitch | queryByTaskId                                                         | queryList                                                  | delayDspTask       || resultRes
        Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new DelayDspOrder(driverOrderFee: 1000d)) | new DelayDspTask() || 1
    }


    @Unroll
    def "query queryDriverAggregation"() {
        given: "Mock数据"
        driverGateway.query(_, _,_) >> Lists.newArrayList(new Driver())
        filterChain.filter(_, _) >> driverList
        delayDspOrderRepository.queryByTaskId(_, _) >> queryByTaskId
        delayDspCommonQConfig.isReachStandardCitySwitch(_) >> reachStandardCitySwitch
        grayscaleQConfig.isGrayscaleCity(_) >> dispatcherOrderCitySwitch
        grayscaleQConfig.isGrayscaleCityAndCarTypeId(_, _) >> dispatcherOrderCitySwitch
        delayDspOrderRepository.query(_, _, _) >> Lists.newArrayList()
        supplyOrderGateway.queryByDriver(_, _, _, _, _) >> Lists.newArrayList()
        driverGateway.queryDriverScore(_) >> Lists.newArrayList()
        delayDspOrderRepository.queryByOrderIds(_) >> queryList
        when: "执行校验方法"
        def res = driverServiceImpl.queryDriverAggregation(driverProfitDayBaselineValue, delayDspTask)

        then: "验证校验结果"
        res.size() == resultRes
        where:
        driverProfitDayBaselineValue               | driverList                                    | reachStandardCitySwitch | dispatcherOrderCitySwitch | queryByTaskId                                                         | queryList                                                  | delayDspTask                     || resultRes
        new DriverProfitDayBaselineValue(100, 200) | null                                          | null                    | null                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask()               || 0
        new DriverProfitDayBaselineValue(100, 200) | Lists.newArrayList(new Driver(driverId: "1")) | true                    | true                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask(taskType: "KM") || 1
        new DriverProfitDayBaselineValue(100, 200) | Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask()               || 1
        null                                       | Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask()               || 1
        new DriverProfitDayBaselineValue(0, 200)   | Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new DelayDspOrder(driverOrderFee: 1000d)) | new DelayDspTask(taskType: null) || 1
        new DriverProfitDayBaselineValue(100, 200) | Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new DelayDspOrder(driverOrderFee: 1000d)) | new DelayDspTask(taskType: "DP") || 0
        new DriverProfitDayBaselineValue(100, 200) | Lists.newArrayList(new Driver(driverId: "1")) | false                   | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new DelayDspOrder(driverOrderFee: 1000d)) | new DelayDspTask(taskType: "DP") || 1
    }



    @Unroll
    def "query queryDriverAggregationForInsert"() {
        given: "Mock数据"
        driverGateway.query(_, _,_) >> Lists.newArrayList(new Driver())
        filterChain.filter(_, _) >> driverList
        delayDspOrderRepository.queryByTaskId(_, _) >> queryByTaskId
        delayDspCommonQConfig.isReachStandardCitySwitch(_) >> reachStandardCitySwitch
        grayscaleQConfig.isGrayscaleCity(_) >> dispatcherOrderCitySwitch
        grayscaleQConfig.isGrayscaleCityAndCarTypeId(_, _) >> dispatcherOrderCitySwitch
        delayDspOrderRepository.query(_, _, _) >> Lists.newArrayList()
        supplyOrderGateway.queryByDriver(_, _, _, _, _) >> Lists.newArrayList()
        driverGateway.queryDriverScore(_) >> Lists.newArrayList()
        delayDspOrderRepository.queryByOrderIds(_) >> queryList
        when: "执行校验方法"
        def res = driverServiceImpl.queryDriverAggregationForInsert(delayDspTask)

        then: "验证校验结果"
        res.size() == resultRes
        where:
        driverList                                    | reachStandardCitySwitch | dispatcherOrderCitySwitch | queryByTaskId                                                         | queryList                                                  | delayDspTask       || resultRes
        null                                          | null                    | null                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 0
        Lists.newArrayList(new Driver(driverId: "1")) | false                   | true                      | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | false                   | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList()                                                  | Lists.newArrayList()                                       | new DelayDspTask() || 1
        Lists.newArrayList(new Driver(driverId: "1")) | true                    | false                     | Lists.newArrayList(new DelayDspOrder(driverId: "1", orderId: "2222")) | Lists.newArrayList(new SupplyOrder(driverOrderFee: 1000d)) | new DelayDspTask() || 1
    }

    @Unroll
    def "query mergeMaps"() {
        given: "Mock数据"
        List<Map<String, List<String>>> maps = Lists.newArrayList()
        Map<String, List<String>> map1 = Maps.newHashMap()
        map1.put("1", Lists.newArrayList("1","2"))
        map1.put("3", Lists.newArrayList("3","4"))

        Map<String, List<String>> map2 = Maps.newHashMap()
        map2.put("1", Lists.newArrayList("5","6"))
        map2.put("3", Lists.newArrayList("3","4"))


        Map<String, List<String>> map3 = Maps.newHashMap()
        map3.put("7", Lists.newArrayList("7","8"))
        map3.put("3", Lists.newArrayList("1","2"))
        maps.add(map1)
        maps.add(map2)
        maps.add(map3)
        when: "执行校验方法"
        def res = driverServiceImpl.mergeMaps(maps)

        then: "验证校验结果"
        res.size() == 3
    }
}
