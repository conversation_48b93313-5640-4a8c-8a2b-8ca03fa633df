package com.ctrip.dcs.dsp.delay.carconfig

import com.ctrip.dcs.dsp.delay.cache.DistributedCache
import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspTaskKey
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService
import com.ctrip.dcs.dsp.delay.service.SupplyOrderService
import com.ctrip.dcs.dsp.delay.service.TakenOrderService
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.JsonUtil
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification
import spock.lang.Unroll



/**
 * <AUTHOR>
 */

class DelayTaskConfigTest extends Specification {




    def delayTaskConfig = new DelayTaskConfig()
    def delayTaskConfigV2 = Mock(DelayTaskConfigV2)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def setup() {
        DelayTaskConfig.Config conf = new DelayTaskConfig.Config();
        conf.setKey(new DelayDspTaskKey(1, 117))
        conf.setValues(Lists.newArrayList(new DelayDspTaskValue(begin: "12:00", end: "14:00", hour: 10, type: "KM")))
        def map1 = ImmutableMap.builder()
                .put("1#0", conf)
                .build();
        delayTaskConfig.delayTaskConfigV2 = delayTaskConfigV2
        delayTaskConfig.delayDspCommonQConfig = delayDspCommonQConfig
        delayTaskConfig.map = map1
    }


    @Unroll
    def "test"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 0
        when: "执行校验方法"
        def res = delayTaskConfig.get(cityId, carTypeId, date,0)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        cityId | carTypeId | date                        || result
        1      | 117       | DateUtil.parseTime("13:00") || 10
        1      | 117       | DateUtil.parseTime("10:00") || null
        1      | 118       | DateUtil.parseTime("13:00") || null
        2      | 118       | DateUtil.parseTime("13:00") || null
    }

    @Unroll
    def "test getAnyType"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 0

        when: "执行校验方法"
        def res = delayTaskConfig.getAnyType(1, 117, DateUtil.parseTime("13:00"), type)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        type         || result
        []           || null
        ["KM"]       || 10
        ["DP"]       || null
        ["DP", "KM"] || null
    }



    @Unroll
    def "test2"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 1
        when: "执行校验方法"
        def res = delayTaskConfig.get(cityId, carTypeId, date,0)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        cityId | carTypeId | date                        || result
        1      | 117       | DateUtil.parseTime("13:00") || null
        1      | 117       | DateUtil.parseTime("10:00") || null
        1      | 118       | DateUtil.parseTime("13:00") || null
        2      | 118       | DateUtil.parseTime("13:00") || null
    }
}
