package com.ctrip.dcs.dsp.delay.service.impl

import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter
import com.ctrip.dcs.dsp.delay.limit.TakenRateLimiter
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.model.DispatchResult
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.Frozen
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.Lists
import com.google.common.util.concurrent.RateLimiter
import io.dropwizard.metrics5.MetricRegistry
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.Executors

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])
class SupplyOrderServiceImplSpec extends Specification {

    def gateway = Mock(SupplyOrderGateway)

    def repo = Mock(DelayDspTaskRepository)

    def dspOrderRepository = Mock(DelayDspOrderRepository)
    def delayDspMatchTaskRepository = Mock(DelayDspMatchTaskRepository)


    def driverGateway = Mock(DriverGateway)

    def inventoryGateway = Mock(InventoryGateway)

    def outPoolRateLimiter = Mock(OutPoolRateLimiter)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def messageProducer = Mock(MessageProducer)

    def takenThreadPool = Executors.newSingleThreadExecutor()

    def redispatchThreadPool = Executors.newSingleThreadExecutor()

    def service = new SupplyOrderServiceImpl(delayDspMatchTaskRepository: delayDspMatchTaskRepository, supplyOrderGateway: gateway, delayDspTaskRepository: repo, inventoryGateway: inventoryGateway, outPoolRateLimiter: outPoolRateLimiter, takenThreadPool: takenThreadPool, redispatchThreadPool: redispatchThreadPool, dspOrderRepository: dspOrderRepository, driverGateway: driverGateway, delayDspCommonQConfig: delayDspCommonQConfig, messageProducer: messageProducer)

    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }


    @Unroll
    def "taken"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1, executeTime: new Date(), executeTimeDeadline: DateUtil.addHours(new Date(), 3))
        outPoolRateLimiter.getRedispatchRateLimiter(task) >> RateLimiter.create(1)
        outPoolRateLimiter.getConfirmRateLimiter(task) >> RateLimiter.create(1)
        gateway.taken(_, _, _, _) >> taken
        inventoryGateway.batchFrozen(_) >> Lists.newArrayList(new Frozen(order: new DelayDspOrder(orderId: "1", isDelay: 1, isCancel: 0), driver: new Driver(), frozen: 1))

        when: "执行校验方法"
        service.taken(task, detail.getOrder(), detail.getDriver(), taskRecord)

        then: "验证校验结果"

        then: "验证校验结果"
        code

        where:

        taskRecord                   | detail                                                                                                                           | taken || code
        new DelayDspTaskRecord() | new DispatchResultDetail(order: new DelayDspOrder(orderId: "1", isDelay: 1, isCancel: 0), driver: new Driver(), matchSuccess: 1) | 500   || true
        new DelayDspTaskRecord() | new DispatchResultDetail(order: new DelayDspOrder(orderId: "1", isDelay: 1, isCancel: 0), driver: new Driver(), matchSuccess: 1) | 0     || true
    }

    @Unroll
    def "redispatch"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1, executeTime: new Date(), executeTimeDeadline: DateUtil.addHours(new Date(), 3))
        outPoolRateLimiter.getRedispatchRateLimiter(task) >> RateLimiter.create(1)
        outPoolRateLimiter.getConfirmRateLimiter(task) >> RateLimiter.create(1)
        gateway.query("1") >> new SupplyOrder(orderId: "1", sourceOrderId: "1")
        gateway.query("2") >> null
        gateway.query("3") >> new SupplyOrder(orderId: "3", sourceOrderId: "3", orderStatus: 3, driverId: "1")
        inventoryGateway.batchFrozen(_) >> Lists.newArrayList(new Frozen(order: new DelayDspOrder(orderId: "1", isDelay: 1, isCancel: 0), driver: new Driver(), frozen: 1))

        when: "执行校验方法"
        service.redispatch(task, detail.getOrder(),taskRecord)

        then: "验证校验结果"

        then: "验证校验结果"
        code

        where:

        taskRecord | detail                                                                                                                                             | taken || code
        new DelayDspTaskRecord() | new DispatchResultDetail(order: new DelayDspOrder(orderId: "1", mainOrderId: "1", isDelay: 1, isCancel: 0), driver: new Driver(), matchSuccess: 1) | 500   || true
        new DelayDspTaskRecord() | new DispatchResultDetail(order: new DelayDspOrder(orderId: "2", mainOrderId: "2", isDelay: 1, isCancel: 0), driver: new Driver(), matchSuccess: 1) | 0     || true
        new DelayDspTaskRecord() | new DispatchResultDetail(order: new DelayDspOrder(orderId: "3", mainOrderId: "3", isDelay: 1, isCancel: 0), driver: new Driver(), matchSuccess: 1) | 0     || true
    }

    @Unroll
    def "taken record"() {

        given: "Mock数据"
        dspOrderRepository.queryByOrderId(_) >> order
        repo.queryByTaskId(_) >> task
        driverGateway.query(_) >> driver
        when: "执行校验方法"
        service.taken(new DelayDspTaskRecord(driverId: driverId, "preOut": preOut))

        then: "验证校验结果"
        true

        where:

        task               | order                         | driverId | driver       | preOut
        null               | null                          | null     | null         | 0
        new DelayDspTask() | null                          | null     | null         | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 0) | null     | null         | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | null     | null         | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "0"      | null         | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "123"    | null         | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "123"    | new Driver() | 0
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "123"    | null         | 1

    }

    @Unroll
    def "redispatch record"() {

        given: "Mock数据"
        dspOrderRepository.queryByOrderId(_) >> order
        repo.queryByTaskId(_) >> task
        driverGateway.query(_) >> driver
        when: "执行校验方法"
        service.redispatch(new DelayDspTaskRecord(driverId: driverId))

        then: "验证校验结果"
        true

        where:

        task               | order                         | driverId | driver
        null               | null                          | null     | null
        new DelayDspTask() | null                          | null     | null
        new DelayDspTask() | new DelayDspOrder(isDelay: 0) | null     | null
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | null     | null
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "0"      | null
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "123"    | null
        new DelayDspTask() | new DelayDspOrder(isDelay: 1) | "123"    | new Driver()
    }

    @Unroll
    def "test delay unfrozen"() {
        given: "Mock数据"
        delayDspCommonQConfig.getDelayUnfrozenMillisecond() >> 10000L
        def order = new DelayDspOrder(orderId: "1")
        def driver = new Driver(driverId: "1")

        when: "执行校验方法"
        service.delayUnfrozen(order, driver)

        then: "验证校验结果"
        true
    }
}
