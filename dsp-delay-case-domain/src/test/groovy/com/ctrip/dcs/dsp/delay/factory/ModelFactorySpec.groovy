package com.ctrip.dcs.dsp.delay.factory

import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.context.DelayDspContext
import com.ctrip.dcs.dsp.delay.enums.CategoryCode
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.DriverScore
import com.ctrip.dcs.dsp.delay.model.Duid
import com.ctrip.dcs.dsp.delay.model.PredictPriceInfo
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class ModelFactorySpec extends Specification{

    @Unroll
    def "build delay dsp task"() {

        given: "Mock数据"
        def conf = new DelayDspTaskValue(taskSeq: 0, begin: "20:00", end: "07:59", hour: 10, deadline: 7)
        def conf1 = new DelayDspTaskValue(taskSeq: 0, begin: "20:00", end: "07:59", hour: 10, deadline: 7,type: "SD")

        when: "执行校验方法"
        def task = ModelFactory.buildDelayDspTask("beijing_city", 1, 117, DateUtil.parseDate("2022-05-19 01:00:00"), conf)
        def task2 = ModelFactory.buildDelayDspTask("beijing_city", 1, 117, DateUtil.parseDate("2022-05-19 22:00:00"), conf)
        def task3 = ModelFactory.buildDelayDspTask("beijing_city", 1, 117, DateUtil.parseDate("2022-05-19 22:00:00"), conf1)

        then: "验证校验结果"
        with(task) {
            cityCode == "beijing_city"
            cityId == 1
            carTypeId == 117
            taskStatus == 0
            taskSeq == 0
            beginTime == DateUtil.parseDate("2022-05-18 20:00:00")
            endTime == DateUtil.parseDate("2022-05-19 07:59:59")
            executeTime == DateUtil.parseDate("2022-05-18 10:00:00")
            executeTimeDeadline == DateUtil.addHours(executeTime, conf.getHour() - conf.getDeadline())
        }
        with(task2) {
            cityCode == "beijing_city"
            cityId == 1
            carTypeId == 117
            taskStatus == 0
            taskSeq == 0
            beginTime == DateUtil.parseDate("2022-05-19 20:00:00")
            endTime == DateUtil.parseDate("2022-05-20 07:59:59")
            executeTime == DateUtil.parseDate("2022-05-19 10:00:00")
            executeTimeDeadline == DateUtil.addHours(executeTime, conf.getHour() - conf.getDeadline())
        }
        with(task3) {
            cityCode == "beijing_city"
            cityId == 1
            carTypeId == 117
            taskStatus == 0
            taskSeq == 0
            beginTime == DateUtil.parseDate("2022-05-19 20:00:00")
            endTime == DateUtil.parseDate("2022-05-20 07:59:59")
            executeTime == DateUtil.parseDate("2022-05-19 12:00:00")
            executeTimeDeadline == DateUtil.addHours(executeTime, conf.getHour() - conf.getDeadline())
        }
    }

    @Unroll
    def "build delay dsp order"() {

        given: "Mock数据"
        def price = new PredictPriceInfo(kiloLength: 10.0, drivTotalFee: 99.99)
        def supplyOrder = new SupplyOrder(orderId: "123", sourceOrderId: "456", cityCode: "beijing_city", cityId: 1,
                carTypeId: 117, skuId: 1, distributionChannel: 1, sysExpectBookTime: DateUtil.parseDate("2022-05-19 12:00:00"),
        predicServiceStopTime: DateUtil.parseDate("2022-05-19 13:00:00"), categoryCode: CategoryCode.airport_dropoff.name(),
        isOtaBookOrder: 0, actualFromLongitude: 1.1, actualFromLatitude: 1.2, actualFromCoordsys: "a", actualToLongitude: 2.1,
        actualToLatitude: 2.2, actualToCoordsys: "b", driverId: "1", orderStatus: 2, predictPriceInfo: price)
        def task = new DelayDspTask(taskId: 1)
        def subsku = new DelayDspSubSkuValue(outSubSkuIds: "8883")

        when: "执行校验方法"
        def order = ModelFactory.buildDelayDspOrder(supplyOrder, task, subsku)

        then: "验证校验结果"
        with(order) {
            orderId == "123"
            mainOrderId == "456"
            cityCode == "beijing_city"
            cityId == 1
            carTypeId == 117
            skuId == 1
            channelId == 1
            sysExpectBookTime == DateUtil.parseDate("2022-05-19 12:00:00")
            predictServiceStopTime == DateUtil.parseDate("2022-05-19 13:00:00")
            categoryCode == CategoryCode.airport_dropoff.name()
            isOta == 0
            fromLongitude == 1.1
            fromLatitude == 1.2
            fromCoordsys == "a"
            fromHash == GeoHashUtil.buildGeoHash(1.1, 1.2)
            toLongitude == 2.1
            toLatitude == 2.2
            toCoordsys == "b"
            toHash == GeoHashUtil.buildGeoHash(2.1, 2.2)
            driverId == "1"
            isCancel == 0
            isDelay == 1
            kiloLength == 10.0
            driverOrderFee == 99.99
//            duid == new Duid(orderId, 8883).toString()
        }
    }

    @Unroll
    def "build positions"() {

        given: "Mock数据"
        def task = new DelayDspTask(endTime: DateUtil.parseTime("12:00"))
        def order = new DelayDspOrder(orderId: "1", fromHash: "1", toHash: "2", sysExpectBookTime: DateUtil.parseTime("11:00"), predictServiceStopTime: DateUtil.parseTime("12:00"), isCancel: 0, isDelay: 1)
        def orders = Lists.newArrayList(
                new DelayDspOrder(orderId: "1", fromHash: "1", toHash: "2", predictServiceStopTime: DateUtil.parseTime("12:00"), isCancel: 0, isDelay: 1),
                new DelayDspOrder(orderId: "2", fromHash: "2", toHash: "2", predictServiceStopTime: DateUtil.parseTime("13:00"), isCancel: 0, isDelay: 1),
                new DelayDspOrder(orderId: "3", fromHash: "3", toHash: "3", predictServiceStopTime: DateUtil.parseTime("11:00"), isCancel: 0, isDelay: 1),
        )
        def drivers = Lists.newArrayList(
                new Driver(addressLongitude: 1.1, addressLatitude: 1.2),
                new Driver(addressLongitude: 2.1, addressLatitude: 2.2)
        )

        when: "执行校验方法"
        def positions = ModelFactory.buildPositions(task, order, orders, drivers)

        then: "验证校验结果"
        positions.size() == 5
    }

    @Unroll
    def "build dispatch result details"() {

        given: "Mock数据"
        def context = new DelayDspContext(delayDspTask: new DelayDspTask(taskId: 1))
        def driverAggregation = new DriverAggregation(driver: new Driver(addressLongitude: 1.1, addressLatitude: 2.2))
        def orders = Lists.newArrayList(new DelayDspOrder(orderId: "1"), new DelayDspOrder(orderId: "2"), new DelayDspOrder(orderId: "3"))
        def gateway = Mock(GeoGateway)

        gateway.queryRoute(_, _) >> new Route("a", 0.0, 0.0)

        when: "执行校验方法"
        def details = ModelFactory.buildDispatchResultDetails(context, driverAggregation, orders, gateway)

        then: "验证校验结果"
        details.size() == 3
    }

    @Unroll
    def "build record"() {

        given: "Mock数据"
        def task = new DelayDspTask()
        def driver = new Driver()
        def score = new DriverScore()
        def empty = new Route()
        def details = Lists.newArrayList(new DispatchResultDetail(order: new DelayDspOrder(isDelay: 0), matchSuccess: 1, driver: driver, driverScore: score, empty: empty), new DispatchResultDetail(order: new DelayDspOrder(isDelay: 1), matchSuccess: 0, driver: driver, driverScore: score, empty: empty))

        when: "执行校验方法"
        def records = ModelFactory.buildDelayDspTaskRecords(task, details)

        then: "验证校验结果"
        for (int i = 0; i < 2; i++) {
            def record = records.get(i)
            def detail = details.get(i)
            record.isOut != detail.order.isDelay
        }
    }
}
