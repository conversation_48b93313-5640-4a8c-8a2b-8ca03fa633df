package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.enums.XSkuCategoryCode
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import org.assertj.core.util.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class PickUpCardValidatorSpec extends Specification {

    def gateway = Mock(SupplyOrderGateway)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def grayscaleQConfig = Mock(GrayscaleQConfig)

    def selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)

    def validator = new PickUpCardValidator(supplyOrderGateway: gateway, grayscaleQConfig: grayscaleQConfig, selfDispatcherOrderGateway: selfDispatcherOrderGateway)

    @Unroll
    def "validate"() {

        given: "Mock数据"
        gateway.queryOrderXSkuCode(_) >> xSkus
        selfDispatcherOrderGateway.flowSwitch(_, _) >> flag

        when: "执行校验方法"
        def res = validator.validate(new SupplyOrder(orderPackageServiceCodes: xSkus))

        then: "验证校验结果"
        res.getCode() == code

        where:
        xSkus                                                                                 | flag  || code
        newSet(XSkuCategoryCode.PICK_UP_CARD.getCode())                                       | false || ValidatorCode.PICK_UP_CARD_LIMIT
        newSet(XSkuCategoryCode.CHILDSEAT.getCode())                                          | false || ValidatorCode.OK
        newSet(XSkuCategoryCode.PICK_UP_CARD.getCode(), XSkuCategoryCode.CHILDSEAT.getCode()) | false || ValidatorCode.PICK_UP_CARD_LIMIT
        newSet()                                                                              | false || ValidatorCode.OK
        newSet(XSkuCategoryCode.PICK_UP_CARD.getCode())                                       | true || ValidatorCode.PICK_UP_CARD_LIMIT
        newSet(XSkuCategoryCode.CHILDSEAT.getCode())                                          | true || ValidatorCode.OK
        newSet(XSkuCategoryCode.PICK_UP_CARD.getCode(), XSkuCategoryCode.CHILDSEAT.getCode()) | true || ValidatorCode.PICK_UP_CARD_LIMIT
        newSet()                                                                              | true || ValidatorCode.OK
    }

    def newSet(String... s) {
        Set<String> set = Sets.newHashSet();
        Iterator<String> iterator = s.iterator()
        while (iterator.hasNext()) {
            set.add(iterator.next())
        }
        return set
    }
}
