package com.ctrip.dcs.dsp.delay.service.impl

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue
import com.ctrip.dcs.dsp.delay.conflict.Check
import com.ctrip.dcs.dsp.delay.conflict.CheckChain
import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.impl.EmptyDurationCheck
import com.ctrip.dcs.dsp.delay.conflict.impl.HeadOrderCheck
import com.ctrip.dcs.dsp.delay.conflict.impl.PrevMileageCheck
import com.ctrip.dcs.dsp.delay.conflict.impl.ServiceTimeCheck
import com.ctrip.dcs.dsp.delay.conflict.impl.TailOrderCheck
import com.ctrip.dcs.dsp.delay.context.DelayDspContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.DriverScore
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspChecksQConfig
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.Lists
import io.dropwizard.metrics5.MetricRegistry
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll


/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])
class DispatchServiceImplSpec extends Specification {

    def delayDspChecksQConfig = Mock(DelayDspChecksQConfig)
    def geoGateway = Mock(GeoGateway)
    def checkChain = new CheckChain(delayDspChecksQConfig: delayDspChecksQConfig)
    def conflictService = new ConflictServiceImpl(checkChain: checkChain)
    def service = new DispatchServiceImpl(conflictService: conflictService, geoGateway: geoGateway)
    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }

    @Unroll
    def "dispatch"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"))
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: HeadTailLimitValue.DEFAULT)
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        geoGateway.queryRoute(_, _) >> route
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver                | order       | route                                  || result
        null                  | getOrders() | new Route(duration: 0D, distance: 0D)  || 0
        getDrivers()          | getOrders() | new Route(duration: 0D, distance: 0D)  || 4    // (2,4),(1,5)
        getDrivers()          | getOrders() | new Route(duration: 5D, distance: 10D) || 3    // (1,4),(5)
        getDriversHaveOrder() | getOrders() | new Route(duration: 0D, distance: 0D)  || 4    // (1,0),(2,4)
        getDriversHaveOrder() | getOrders() | new Route(duration: 5D, distance: 10D) || 3    // (0),(1,4)
    }

    @Unroll
    def "o2首单不达标，但是可以和o1、o3匹配"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"))
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        def headOrderCheck = Mock(HeadOrderCheck)
        headOrderCheck.check(_) >> CheckCode.OK >> CheckCode.HEAD_LIMIT_CONFLICT >> CheckCode.OK
        geoGateway.queryRoute(_, _) >> new Route(duration: 0D, distance: 0D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(headOrderCheck)


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers1() | getOrders3() || 3    // (1,2,3)
    }

    @Unroll
    def "o1、o2首单不达标，o3独自接单"() {
        given: "Mock数据"

        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"))
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)

        geoGateway.queryRoute(_, _) >> new Route(duration: 20D, distance: 5D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))



        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers() | getOrders1()  || 1    // (3)
    }

    @Unroll
    def "o1、o2首单不达标，且互相冲突但o1、o2是已接订单，o3普通延后派订单"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"))
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)

        geoGateway.queryRoute(_, _) >> new Route(duration: 20D, distance: 5D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))

        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers2() | getOrders2() || 3    // (1,2,3)
    }

    @Unroll
    def "o1、o2、o3匹配，但o3不能作为尾单"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"))
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        def tailOrderCheck = Mock(TailOrderCheck)
        tailOrderCheck.check(_) >> CheckCode.TAIL_LIMIT_CONFLICT >> CheckCode.OK >> CheckCode.OK
        geoGateway.queryRoute(_, _) >> new Route(duration: 0D, distance: 0D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(tailOrderCheck)
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers1() | getOrders3() || 2    // (1,2)
    }


    def getDrivers1() {
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList())
        return Lists.newArrayList(aggregation1)
    }

    def getDrivers2() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54, driverId: "1", isDelay: 0)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("09:02"), predictServiceStopTime: DateUtil.parseTime("09:47"), kiloLength: 0, driverOrderFee: 96.24, driverId: "1", isDelay: 0)
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList(o1, o2))
        return Lists.newArrayList(aggregation1)
    }

    def getDrivers() {
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def driver2 = new Driver(driverId: "2", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def score2 = new DriverScore(totalScore: 10D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList())
        def aggregation2 = new DriverAggregation(driver: driver2, score: score2, orders: Lists.newArrayList())

        return Lists.newArrayList(aggregation1, aggregation2)
    }

    def getDriversHaveOrder() {
        def order = new DelayDspOrder(orderId: "0", sysExpectBookTime: DateUtil.parseTime("08:43"), predictServiceStopTime: DateUtil.parseTime("09:48"), kiloLength: 0, driverOrderFee: 75.54, driverId: "1", isDelay: 0)
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def driver2 = new Driver(driverId: "2", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def score2 = new DriverScore(totalScore: 10D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList(order))
        def aggregation2 = new DriverAggregation(driver: driver2, score: score2, orders: Lists.newArrayList())

        return Lists.newArrayList(aggregation1, aggregation2)
    }

    def getOrders() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:02"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("08:35"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120)
        def o4 = new DelayDspOrder(orderId: "4", sysExpectBookTime: DateUtil.parseTime("09:01"), predictServiceStopTime: DateUtil.parseTime("09:32"), kiloLength: 0, driverOrderFee: 132.57)
        def o5 = new DelayDspOrder(orderId: "5", sysExpectBookTime: DateUtil.parseTime("08:42"), predictServiceStopTime: DateUtil.parseTime("09:48"), kiloLength: 0, driverOrderFee: 162)
        def o6 = new DelayDspOrder(orderId: "6", sysExpectBookTime: DateUtil.parseTime("08:23"), predictServiceStopTime: DateUtil.parseTime("10:51"), kiloLength: 0, driverOrderFee: 160.05)
        def o7 = new DelayDspOrder(orderId: "7", sysExpectBookTime: DateUtil.parseTime("07:23"), predictServiceStopTime: DateUtil.parseTime("08:51"), kiloLength: 0, driverOrderFee: 160.05)
        def o8 = new DelayDspOrder(orderId: "8", sysExpectBookTime: DateUtil.parseTime("21:23"), predictServiceStopTime: DateUtil.parseTime("22:51"), kiloLength: 0, driverOrderFee: 160.05)
        return Lists.newArrayList(o1, o2, o3, o4, o5, o6, o7, o8)
    }

    def getOrders1() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:02"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("08:35"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120)
        return Lists.newArrayList(o1, o2, o3)
    }


    def getOrders2() {
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("10:35"), predictServiceStopTime: DateUtil.parseTime("10:30"), kiloLength: 0, driverOrderFee: 120)
        return Lists.newArrayList(o3)
    }

    def getOrders3() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:18"), kiloLength: 0, driverOrderFee: 75.54)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:32"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("09:05"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120)
        return Lists.newArrayList(o1, o2, o3)
    }

}
