//package com.ctrip.dcs.dsp.delay.validator;
//
//import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway;
//import com.ctrip.dcs.dsp.delay.model.ChannelNumber;
//import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
//import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
//import com.ctrip.dcs.dsp.delay.validator.impl.ChannelValidator;
//import com.google.common.collect.Sets;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.modules.junit4.PowerMockRunner;
//
///**
// * <AUTHOR>
// */
//@RunWith(PowerMockRunner.class)
//public class ChannelValidatorTest {
//
//    @InjectMocks
//    private ChannelValidator channelValidator;
//
//    @Mock
//    private ChannelGateway channelGateway;
//
//    @Mock
//    private DelayDspCommonQConfig delayDspCommonQConfig;
//
//    @Test
//    public void testError() {
//        SupplyOrder order = new SupplyOrder();
//        PowerMockito.when(channelGateway.queryChannelNumber(Mockito.any())).thenReturn(null);
//        ValidatorCode code = channelValidator.validate(order);
//        Assert.assertEquals(ValidatorCode.ERROR, code);
//    }
//
//    @Test
//    public void testInBlackList() {
//        SupplyOrder order = new SupplyOrder();
//        ChannelNumber channel = new ChannelNumber();
//        channel.setChannelNumberId(1L);
//        channel.setPrimaryChannelGroupId(2L);
//        channel.setSecondaryChannelGroupId(3L);
//        channel.setTertiaryChannelGroupId(4L);
//        PowerMockito.when(channelGateway.queryChannelNumber(Mockito.any())).thenReturn(channel);
//        PowerMockito.when(delayDspCommonQConfig.getNotDelayDspChannelSet()).thenReturn(Sets.newHashSet(1L,4L,6L,7L));
//        ValidatorCode code = channelValidator.validate(order);
//        Assert.assertEquals(ValidatorCode.CHANNEL, code);
//    }
//
//    @Test
//    public void testNotInWhiteList() {
//        SupplyOrder order = new SupplyOrder();
//        ChannelNumber channel = new ChannelNumber();
//        channel.setChannelNumberId(1L);
//        channel.setPrimaryChannelGroupId(2L);
//        channel.setSecondaryChannelGroupId(3L);
//        channel.setTertiaryChannelGroupId(4L);
//        PowerMockito.when(channelGateway.queryChannelNumber(Mockito.any())).thenReturn(channel);
//        PowerMockito.when(delayDspCommonQConfig.getNotDelayDspChannelSet()).thenReturn(Sets.newHashSet(6L,7L));
//        PowerMockito.when(delayDspCommonQConfig.getDelayDspChannelSet()).thenReturn(Sets.newHashSet(9L));
//        ValidatorCode code = channelValidator.validate(order);
//        Assert.assertEquals(ValidatorCode.CHANNEL, code);
//    }
//
//    @Test
//    public void test() {
//        SupplyOrder order = new SupplyOrder();
//        ChannelNumber channel = new ChannelNumber();
//        channel.setChannelNumberId(1L);
//        channel.setPrimaryChannelGroupId(2L);
//        channel.setSecondaryChannelGroupId(3L);
//        channel.setTertiaryChannelGroupId(4L);
//        PowerMockito.when(channelGateway.queryChannelNumber(Mockito.any())).thenReturn(channel);
//        PowerMockito.when(delayDspCommonQConfig.getNotDelayDspChannelSet()).thenReturn(Sets.newHashSet(6L,7L));
//        PowerMockito.when(delayDspCommonQConfig.getDelayDspChannelSet()).thenReturn(Sets.newHashSet(2L));
//        ValidatorCode code = channelValidator.validate(order);
//        Assert.assertEquals(ValidatorCode.OK, code);
//    }
//}
