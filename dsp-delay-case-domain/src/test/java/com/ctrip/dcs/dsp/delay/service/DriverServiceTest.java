package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.filter.FilterChain;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.service.impl.DriverServiceImpl;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class DriverServiceTest {

    @InjectMocks
    private DriverServiceImpl driverService;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private FilterChain filterChain;

    @Mock
    private SupplyOrderGateway supplyOrderGateway;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private GrayscaleQConfig grayscaleQConfig;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Test
    public void testQueryDriver() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DelayDspTask task = new DelayDspTask();
        PowerMockito.when(driverGateway.query(Mockito.anyInt(), Mockito.anyInt(),Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(filterChain.filter(Mockito.anyList(), Mockito.any())).thenReturn(Lists.newArrayList());
        List<Driver> drivers = driverService.queryDriver(task);
        Assert.assertEquals(0, drivers.size());
    }

    @Test
    public void testQueryDriverAggregation() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SupplyOrder outOrder = new SupplyOrder();
        outOrder.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setBeginTime(new Date());
        task.setEndTime(new Date());
        Driver driver = new Driver();
        driver.setDriverId("1");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(false);
        PowerMockito.when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.FALSE);
        PowerMockito.when(driverGateway.query(Mockito.anyInt(), Mockito.anyInt(),Mockito.any())).thenReturn(Lists.newArrayList(driver));
        PowerMockito.when(driverGateway.queryDriverScore(Mockito.anySet())).thenReturn(Lists.newArrayList());
        PowerMockito.when(filterChain.filter(Mockito.anyList(), Mockito.any())).thenReturn(Lists.newArrayList(driver));
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(supplyOrderGateway.queryByDriver(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(outOrder));
        List<DriverAggregation> drivers = driverService.queryDriverAggregation(null, task);
        Assert.assertEquals(1, drivers.size());
    }

    @Test
    public void testQueryDriverAggregation1() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SupplyOrder outOrder = new SupplyOrder();
        outOrder.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setBeginTime(new Date());
        task.setEndTime(new Date());
        Driver driver = new Driver();
        driver.setDriverId("1");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        PowerMockito.when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(driverGateway.query(Mockito.anyInt(), Mockito.anyInt(),Mockito.any())).thenReturn(Lists.newArrayList(driver));
        PowerMockito.when(driverGateway.queryDriverScore(Mockito.anySet())).thenReturn(Lists.newArrayList());
        PowerMockito.when(filterChain.filter(Mockito.anyList(), Mockito.any())).thenReturn(Lists.newArrayList(driver));
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(delayDspOrderRepository.query(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(delayDspOrder));
        PowerMockito.when(delayDspOrder.getDriverId()).thenReturn("1");
        PowerMockito.when(supplyOrderGateway.queryByDriver(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(outOrder));
        List<DriverAggregation> drivers = driverService.queryDriverAggregation(null, task);
        Assert.assertEquals(1, drivers.size());
    }

    @Test
    public void testQueryDriverAggregationEmpty() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DelayDspTask task = new DelayDspTask();
        task.setBeginTime(new Date());
        task.setEndTime(new Date());
        PowerMockito.when(driverGateway.query(Mockito.anyInt(), Mockito.anyInt(),Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(driverGateway.queryDriverScore(Mockito.anySet())).thenReturn(Lists.newArrayList());
        PowerMockito.when(filterChain.filter(Mockito.anyList(), Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.when(supplyOrderGateway.queryByDriver(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList());
        List<DriverAggregation> drivers = driverService.queryDriverAggregation(null, task);
        Assert.assertEquals(0, drivers.size());
    }
}
