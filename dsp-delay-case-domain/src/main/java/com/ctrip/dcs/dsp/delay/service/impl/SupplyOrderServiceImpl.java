package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.enums.OutReasonEnum;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.limit.TakenRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.Frozen;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.SupplyOrderService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SupplyOrderServiceImpl implements SupplyOrderService {

    private static final Logger logger = LoggerFactory.getLogger(SupplyOrderServiceImpl.class);

    @Autowired
    private SupplyOrderGateway supplyOrderGateway;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository dspOrderRepository;

    @Autowired
    private TakenRateLimiter limiter;

    @Autowired
    private OutPoolRateLimiter outPoolRateLimiter;

    @Autowired
    private InventoryGateway inventoryGateway;

    @Autowired
    private DriverGateway driverGateway;

    @Resource(name = "takenThreadPool")
    private ExecutorService takenThreadPool;

    @Resource(name = "redispatchThreadPool")
    private ExecutorService redispatchThreadPool;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private DelayDspMatchTaskRepository delayDspMatchTaskRepository;

    /**
     * 批量冻结
     * 返回冻结运力成功的订单
     * @param matchDetails
     * @return 冻结成功的订单号
     */
    @Override
    public List<String> batchFrozen(List<DispatchResultDetail> matchDetails) {
        try {
            List<Frozen> list = matchDetails.stream()
                    .filter(d -> StringUtils.equalsIgnoreCase(d.getOrder().getOrderSource(), OrderSource.QUNAR.name()))
                    .map(d -> new Frozen(d.getOrder(), d.getDriver()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            list = inventoryGateway.batchFrozen(list);
            return list.stream().filter(Frozen::isFrozen).map(Frozen::getOrder).map(DelayDspOrder::getOrderId).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("SupplyOrderServiceImpl.batchFrozen error!", e);
        }
        return Collections.emptyList();
    }

    @Override
    public void redispatch(DelayDspTaskRecord record) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(record.getTaskId());
        logger.info("SupplyOrderServiceImpl_redispatch_queryByTaskId", JsonUtil.toJson(task));
        DelayDspOrder order = dspOrderRepository.queryByOrderId(record.getOrderId());
        logger.info("SupplyOrderServiceImpl_redispatch_queryByOrderId", JsonUtil.toJson(order));
        if (Objects.isNull(task) || Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            return;
        }
        redispatch(task, order, record);
    }


    public void redispatch(DelayDspTask task, DelayDspOrder order, DelayDspTaskRecord record) {
        try {
            MetricsUtil.recordValue("order.match.no", 1);
            try {
                if (isLimit(task)) {
                    double acquire = outPoolRateLimiter.getRedispatchRateLimiter(task).acquire();
                    logger.info("SupplyOrderServiceImpl.redispatch", "get redispatch token.order: {}, time:{}", order.getOrderId(), acquire);
                }
            } catch (Exception e) {
                logger.error("SupplyOrderServiceImpl.redispatch", "redispatch try acquire error! order id:{}", order.getOrderId());
            }
            redispatch(order, record);
        } catch (Exception e) {
            MetricsUtil.recordValue("self.redispatch.error", 1);
            logger.error("SupplyOrderServiceImpl.redispatch", "redispatch error! order id:{}", order.getOrderId());
            updateOut(record.getId(), YesOrNo.NO.getCode());
        }
    }

    @Override
    public void redispatch(DelayDspOrder order, DelayDspTaskRecord record) {
        SupplyOrder supplyOrder = supplyOrderGateway.query(order.getOrderId());
        logger.info("SupplyOrderServiceImpl_redispatch_query", "order: {}, supplyOrder: {}", order.getOrderId(), JsonUtil.toJson(supplyOrder));
        if (Objects.isNull(supplyOrder)) {
            supplyOrderGateway.redispatch(order.getMainOrderId());
            updateDelayDspMatchTaskRecord(record);
            return;
        }
        if (!supplyOrder.isServiceProviderConfirm()) {    // 返查采购订单，若采购订单非服务商确认，则不进行改派
            logger.info("SupplyOrderServiceImpl.redispatch", "order is not service provider confirm.order: {}", supplyOrder.getOrderId());
            return;
        }
        supplyOrderGateway.redispatch(order.getMainOrderId());
        updateDelayDspMatchTaskRecord(record);
    }


    private void updateDelayDspMatchTaskRecord(DelayDspTaskRecord record){
        if(Objects.isNull(record) || record.getPreOut().equals(YesOrNo.NO.getCode())){
            return;
        }
        DelayDspMatchTaskRecord record1 = new DelayDspMatchTaskRecord();
        record1.setId(record.getMatchRecordId());
        record1.setTakenCode(null);
        record1.setOutTime(new Date());
        delayDspMatchTaskRepository.update(record1);
    }




    @Override
    public void taken(DelayDspTaskRecord record) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(record.getTaskId());
        logger.info("SupplyOrderServiceImpl_taken_queryByTaskId", "task: {}", JsonUtil.toJson(task));
        DelayDspOrder order = dspOrderRepository.queryByOrderId(record.getOrderId());
        logger.info("SupplyOrderServiceImpl_taken_queryByOrderId", "order: {}", JsonUtil.toJson(order));
        if (Objects.isNull(task) || Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            logger.warn("SupplyOrderServiceImpl_taken", "task or order is null! task: {}, order: {}", task, order);
            return;
        }
        if (StringUtils.isBlank(record.getDriverId()) || Objects.equals("0", record.getDriverId())) {
            delayDspTaskRepository.updateRecordMatchStatus(YesOrNo.NO.getCode(), YesOrNo.NO.getCode(), record.getId());
            return;
        }
        Driver driver = driverGateway.query(record.getDriverId());
        logger.info("SupplyOrderServiceImpl_taken_queryDriver", "driver: {}", JsonUtil.toJson(driver));
        if (Objects.isNull(driver)) {
            if(record.getPreOut().equals(YesOrNo.NO.getCode())){
                delayDspTaskRepository.updateRecordMatchStatus(YesOrNo.NO.getCode(), YesOrNo.NO.getCode(), record.getId());
            }else {
                DelayDspMatchTaskRecord record1 = new DelayDspMatchTaskRecord();
                record1.setId(record.getMatchRecordId());
                record1.setTakenCode(OrderTakenCode.NO_DRIVER.getCode());
                record1.setOutTime(new Date());
                delayDspMatchTaskRepository.update(record1);
            }
            return;
        }
        taken(task, order, driver, record);
    }

    public OrderTakenCode taken(DelayDspTask task, DelayDspOrder order, Driver driver,DelayDspTaskRecord record) {
        try {
            try {
                if (isLimit(task)) {
                    // 限流
                    double acquire = outPoolRateLimiter.getConfirmRateLimiter(task).acquire();
                    logger.info("SupplyOrderServiceImpl.taken", "get taken token.order: {}, time:{}", order.getOrderId(), acquire);
                }
            } catch (Exception e) {
                logger.error("SupplyOrderServiceImpl_taken", "taken try acquire error! order id:{}", order.getOrderId());
            }
            // 匹配到司机，并接单
            Integer code = supplyOrderGateway.taken(order.getOrderId(), driver.getDriverId(), order.getDuid(), driver.getRegisterTransportGroupId());
            logger.info("SupplyOrderServiceImpl_taken_taken", "order id:{}, taken code:{}", order.getOrderId(), code);
            delayDspTaskRepository.updateTakenCode(code, record);
            // 解冻运力
            delayUnfrozen(order, driver);
            if (!Objects.equals(code, OrderTakenCode.SUCCESS.getCode())) {
                // 接单失败
                MetricsUtil.recordValue("order.match.taken.fail", 1);
                return OrderTakenCode.ERROR;
            }
            MetricsUtil.recordValue("order.match.ok", 1);
        } catch (Exception e) {
            MetricsUtil.recordValue("self.confirm.error", 1);
            logger.error("SupplyOrderServiceImpl.taken error!", e);
            updateOut(record.getId(), YesOrNo.NO.getCode());
            return OrderTakenCode.ERROR;
        }
        return OrderTakenCode.SUCCESS;
    }


    public void delayUnfrozen(DelayDspOrder order, Driver driver) {
        Map<String, Object> map = ImmutableMap.of("orderId", order.getOrderId(), "driverId", driver.getDriverId());
        long delayMillisecond = delayDspCommonQConfig.getDelayUnfrozenMillisecond();
        messageProducer.sendDelayMessage(CommonConstant.DELAY_ORDER_DRIVER_INVENTORY_UNFROZEN, map, delayMillisecond);
    }

    private void updateOut(Long recordId, Integer out) {
        try {
            DelayDspTaskRecord record = new DelayDspTaskRecord();
            record.setId(recordId);
            record.setIsOut(out);
            delayDspTaskRepository.update(record);
        } catch (Exception e) {
            logger.error("SupplyOrderServiceImpl.updateOut", "update out error! record id:{}", recordId);
        }
    }

    /**
     * 是否需要限流
     * 当前时间与最晚出池时间的时间差，若小于30分钟，则不限流
     * @param task
     * @return
     */
    private boolean isLimit(DelayDspTask task) {
        double diff = DateUtil.minutesDiff(new Date(), task.getExecuteTimeDeadline());
        return diff > 30D;
    }
}
