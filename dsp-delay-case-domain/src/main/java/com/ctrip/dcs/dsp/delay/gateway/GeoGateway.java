package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface GeoGateway {

    /**
     * lbs
     * @param cityId
     * @param positions
     * @return
     */
    List<Route> queryRoutes(Integer cityId, List<Position> positions);

    List<Route> queryRoutes(Integer cityId, List<Position> positions, Date useTime, String userOrderId);

    Route queryRoute(Long taskId, Position position);


}
