package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Top20城市配置类
 * 用于配置top20城市列表，支持判断城市是否在top20范围内
 * 
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("top20_city.properties")
public class Top20CityConfig {

    /**
     * top20城市列表，逗号分隔
     * 例如：top20Cities=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
     */
    private String top20Cities;

    /**
     * 判断指定城市是否在top20城市列表中
     * 
     * @param cityId 城市ID
     * @return true-在top20城市列表中，false-不在
     */
    public boolean isTop20City(Integer cityId) {
        if (cityId == null || StringUtils.isBlank(top20Cities)) {
            return false;
        }
        
        List<String> cityList = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(top20Cities);
        
        if (CollectionUtils.isEmpty(cityList)) {
            return false;
        }
        
        return cityList.contains(cityId.toString());
    }

    /**
     * 获取top20城市列表
     * 
     * @return 城市ID列表
     */
    public List<String> getTop20CityList() {
        if (StringUtils.isBlank(top20Cities)) {
            return null;
        }
        
        return Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(top20Cities);
    }
}
