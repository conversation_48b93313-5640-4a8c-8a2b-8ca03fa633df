package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.Getter;
import lombok.Setter;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Route {

    private String hash;

    private Double distance;

    private Double duration;

    public Route() {
    }

    public Route(String hash, double distance, double duration) {
        this.hash = hash;
        this.distance = distance;
        this.duration = duration;
    }

    /**
     * 生成缓存key（原有方法，保持兼容性）
     *
     * @param taskId 任务ID
     * @param hash 位置hash
     * @return 缓存key
     */
    public static String toKey(Long taskId, String hash) {
        return CommonConstant.ADJACENCY_INFO_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + CommonConstant.PLACEHOLDER + taskId + CommonConstant.PLACEHOLDER + hash;
    }

    /**
     * 生成带用车时间维度的缓存key
     *
     * @param taskId 任务ID
     * @param hash 位置hash
     * @param useTime 用车时间
     * @return 缓存key
     */
    public static String toKey(Long taskId, String hash, Date useTime) {
        String hourKey = "";
        if (useTime != null) {
            SimpleDateFormat hourFormat = new SimpleDateFormat("yyyyMMddHH");
            hourKey = hourFormat.format(useTime);
        }
        return CommonConstant.ADJACENCY_INFO_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + CommonConstant.PLACEHOLDER + taskId + CommonConstant.PLACEHOLDER + hash + CommonConstant.PLACEHOLDER + hourKey;
    }

    public static String toValue(Double distance, Double duration) {
        return distance + CommonConstant.PLACEHOLDER + duration;
    }

}
