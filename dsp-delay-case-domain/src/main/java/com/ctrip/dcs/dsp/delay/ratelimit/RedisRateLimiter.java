package com.ctrip.dcs.dsp.delay.ratelimit;

/**
 * Redis分布式限流器接口
 * 基于令牌桶算法实现，不使用Lua脚本
 * <AUTHOR>
 */
public interface RedisRateLimiter {

    /**
     * 尝试获取令牌
     * @param key 限流器的唯一标识
     * @return true表示获取成功，false表示被限流
     */
    boolean tryAcquire(String key);

    /**
     * 尝试获取指定数量的令牌
     * @param key 限流器的唯一标识
     * @param permits 需要获取的令牌数量
     * @return true表示获取成功，false表示被限流
     */
    boolean tryAcquire(String key, int permits);

    /**
     * 尝试在指定时间内获取令牌
     * @param key 限流器的唯一标识
     * @param permits 需要获取的令牌数量
     * @param timeoutMillis 等待超时时间（毫秒）
     * @return true表示获取成功，false表示被限流或超时
     */
    boolean tryAcquire(String key, int permits, long timeoutMillis);

    /**
     * 获取当前可用的令牌数量
     * @param key 限流器的唯一标识
     * @return 当前可用的令牌数量
     */
    long getAvailableTokens(String key);

    /**
     * 重置限流器
     * @param key 限流器的唯一标识
     */
    void reset(String key);

    /**
     * 判断限流器是否启用
     * @return true表示启用，false表示禁用
     */
    boolean isEnabled();
}
