package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SupplyOrderGateway {

    /**
     * 查询采购订单
     * @param orderId
     * @return
     */
    SupplyOrder query(String orderId);

    List<SupplyOrder> queryByDriver(List<String> driverIds);

    List<SupplyOrder> queryByDriver(List<String> driverIds, Date forStartBookTime, Date forEndBookTime, Date backStartBookTime, Date backEndBookTime);

    Integer taken(String orderId, String driverId, String duid, Integer transportGroupId);

    void redispatch(String orderId);

    Set<String> queryOrderXSkuCode(String mainOrderId);
}
