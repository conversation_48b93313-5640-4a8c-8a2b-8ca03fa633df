package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 灰度验证器
 * <AUTHOR>
 */
@Component("grayscaleValidator")
public class GrayscaleValidator implements Validator {

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Autowired
    private GrayscaleQConfig grayscaleQConfig;

    @Autowired
    private DelayTaskConfig delayTaskConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        Boolean isNew = selfDispatcherOrderGateway.flowSwitch(order.getSourceOrderId(), order.getOrderId());
        if (!isNew) {
            // 非新流程
            DelayDspTaskValue value = delayTaskConfig.get(order.getCityId(), order.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
            String taskType = Optional.ofNullable(value).map(DelayDspTaskValue::getType).orElse(StringUtils.EMPTY);
            return StringUtils.equalsIgnoreCase(taskType, DelayDspTaskType.KM.name()) ? new ValidatorDTO(ValidatorCode.GRAYSCALE_KM) : new ValidatorDTO(ValidatorCode.OK);
        }
        Boolean isGrayscaleUid = grayscaleQConfig.isGrayscaleUid(order.getUid());
        if (!isGrayscaleUid) {
            // 非灰度uid
            return new ValidatorDTO(ValidatorCode.GRAYSCALE_UID);
        }
        Boolean isRateLimit = grayscaleQConfig.isGrayscaleCityAndCarTypeIdRateLimit(order.getCityId(), order.getCarTypeId(), order.getOrderId());
        if (!isRateLimit) {
            // 非灰度城市流量比例
            return new ValidatorDTO(ValidatorCode.GRAYSCALE_CITY_RATE);
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
