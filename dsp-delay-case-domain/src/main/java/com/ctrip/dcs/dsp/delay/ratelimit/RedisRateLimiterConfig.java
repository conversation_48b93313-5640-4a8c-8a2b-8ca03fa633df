package com.ctrip.dcs.dsp.delay.ratelimit;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qconfig.QConfig2;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * Redis限流器配置类
 * 从QConfig读取限流相关配置
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("redis_rate_limiter.properties")
public class RedisRateLimiterConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterConfig.class);

    /**
     * 限流器是否启用，默认关闭
     */
    private boolean enabled = false;

    /**
     * 令牌桶容量（最大令牌数）
     */
    private int bucketCapacity = 1000;

    /**
     * 令牌生成速率（每秒生成的令牌数）
     */
    private int tokensPerSecond = 100;

    /**
     * Redis key的过期时间（秒）
     */
    private int keyExpireSeconds = 300;

    /**
     * 获取令牌的默认超时时间（毫秒）
     */
    private long defaultTimeoutMillis = 100;

    /**
     * Redis操作的超时时间（毫秒）
     */
    private long redisTimeoutMillis = 200;

    /**
     * 是否在异常时默认通过
     */
    private boolean passOnException = true;

    /**
     * 令牌桶填充间隔（毫秒）
     */
    private long refillIntervalMillis = 100;

    /**
     * 最小令牌请求数
     */
    private int minTokenRequest = 1;

    /**
     * 最大令牌请求数
     */
    private int maxTokenRequest = 100;

    /**
     * 配置变更监听
     */
    public void onConfigChange() {
        logger.info("RedisRateLimiterConfig changed - enabled: {}, bucketCapacity: {}, tokensPerSecond: {}", 
                    enabled, bucketCapacity, tokensPerSecond);
        validateConfig();
    }

    /**
     * 验证配置的合法性
     */
    private void validateConfig() {
        if (bucketCapacity <= 0) {
            logger.warn("Invalid bucketCapacity: {}, reset to default 1000", bucketCapacity);
            bucketCapacity = 1000;
        }
        if (tokensPerSecond <= 0) {
            logger.warn("Invalid tokensPerSecond: {}, reset to default 100", tokensPerSecond);
            tokensPerSecond = 100;
        }
        if (keyExpireSeconds <= 0) {
            logger.warn("Invalid keyExpireSeconds: {}, reset to default 300", keyExpireSeconds);
            keyExpireSeconds = 300;
        }
        if (defaultTimeoutMillis < 0) {
            logger.warn("Invalid defaultTimeoutMillis: {}, reset to default 100", defaultTimeoutMillis);
            defaultTimeoutMillis = 100;
        }
        if (redisTimeoutMillis <= 0) {
            logger.warn("Invalid redisTimeoutMillis: {}, reset to default 200", redisTimeoutMillis);
            redisTimeoutMillis = 200;
        }
        if (refillIntervalMillis <= 0) {
            logger.warn("Invalid refillIntervalMillis: {}, reset to default 100", refillIntervalMillis);
            refillIntervalMillis = 100;
        }
    }
}
