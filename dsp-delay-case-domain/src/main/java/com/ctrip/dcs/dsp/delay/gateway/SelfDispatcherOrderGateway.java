package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SelfDispatcherOrderGateway {

    Boolean flowSwitch(String userOrderId, String dspOrderId);

    SupplyOrder query(String orderId);

    SupplyOrder queryBase(String orderId);


    SupplyOrder queryByUserOrderId(String userOrderId);

    void redispatch(String userOrderId);

    int taken(String dspOrderId, String driverId, Integer transportGroupId, String duid);

    Map<String, String> queryAvailableDrivers(String orderId, String duid);

    Boolean isRedispatch(String userOrderId);

}
