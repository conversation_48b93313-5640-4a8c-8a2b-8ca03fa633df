package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("taskValidator")
public class TaskValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(TaskValidator.class);

    @Autowired
    private DelayTaskService delayTaskService;

    @Autowired
    private DelayTaskConfig delayTaskConfig;

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private GrayscaleQConfig grayscaleQConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        DelayDspTaskValue value = delayTaskConfig.get(order.getCityId(), order.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
        if (order.isTaken() || order.isSyncKmOrder()) {
            Driver driver = driverGateway.query(order.getDriverId());
            if (Objects.isNull(driver)) {
                logger.info("TaskValidator", "driver is null.order: {}, driver: {}", order.getOrderId(), order.getDriverId());
                return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);
            }
            value = delayTaskConfig.get(driver.getCityId(), driver.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
        }
        if (value == null) {
            logger.info("TaskValidator", "task not exist.order: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);
        }
        Date executeTime = DateUtil.addHours(value.getBeginTime(order.getSysExpectBookTime()), -value.getHour());
        if (DelayDspTaskType.isSD(value.getType())) {
            executeTime = DateUtil.addHours(order.getSysExpectBookTime(), -value.getHour());
        }
        if (!executeTime.after(new Date()) || !executeTime.before(order.getSysExpectBookTime())) {
            logger.info("TaskValidator", "date time before now.execute time:{}", JsonUtil.toJson(executeTime));
            return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);
        }
        if (StringUtils.equalsIgnoreCase(value.getType(), DelayDspTaskType.KM.name()) && !grayscaleQConfig.isGrayscaleCityAndCarTypeId(order.getCityId(), order.getCarTypeId())) {
            logger.info("TaskValidator", "km task off");
            return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);

        }
        return new ValidatorDTO(ValidatorCode.OK);

    }
}
