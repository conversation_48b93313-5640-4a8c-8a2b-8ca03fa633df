package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * 路径接口切换开关配置类
 * 用于配置是否直接切换新接口还是异步监控模式
 * 
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("route_api_switch.properties")
public class RouteApiSwitchConfig {

    /**
     * 全局开关：是否启用新接口
     * true-启用新接口，false-使用旧接口
     */
    private Boolean enableNewApi = false;

    /**
     * 直接切换开关：是否直接切换为新接口
     * true-直接切换，false-异步监控模式
     */
    private Boolean directSwitch = false;

    /**
     * 异步监控开关：是否启用异步监控
     * true-启用异步监控，false-不启用
     */
    private Boolean enableAsyncMonitor = true;

    /**
     * 延迟消息开关：是否启用延迟消息
     * true-启用延迟消息，false-不启用
     */
    private Boolean enableDelayMessage = true;

    /**
     * 缓存开关：是否启用缓存
     * true-启用缓存，false-不启用
     */
    private Boolean enableCache = true;

    /**
     * 监控埋点开关：是否启用监控埋点
     * true-启用监控埋点，false-不启用
     */
    private Boolean enableMonitor = true;

    /**
     * 判断是否应该使用新接口
     * 
     * @return true-使用新接口，false-使用旧接口
     */
    public boolean shouldUseNewApi() {
        return enableNewApi != null && enableNewApi;
    }

    /**
     * 判断是否应该直接切换到新接口
     * 
     * @return true-直接切换，false-异步监控模式
     */
    public boolean shouldDirectSwitch() {
        return directSwitch != null && directSwitch;
    }

    /**
     * 判断是否应该启用异步监控
     * 
     * @return true-启用异步监控，false-不启用
     */
    public boolean shouldEnableAsyncMonitor() {
        return enableAsyncMonitor != null && enableAsyncMonitor;
    }

    /**
     * 判断是否应该启用延迟消息
     * 
     * @return true-启用延迟消息，false-不启用
     */
    public boolean shouldEnableDelayMessage() {
        return enableDelayMessage != null && enableDelayMessage;
    }

    /**
     * 判断是否应该启用缓存
     * 
     * @return true-启用缓存，false-不启用
     */
    public boolean shouldEnableCache() {
        return enableCache != null && enableCache;
    }

    /**
     * 判断是否应该启用监控埋点
     * 
     * @return true-启用监控埋点，false-不启用
     */
    public boolean shouldEnableMonitor() {
        return enableMonitor != null && enableMonitor;
    }
}
