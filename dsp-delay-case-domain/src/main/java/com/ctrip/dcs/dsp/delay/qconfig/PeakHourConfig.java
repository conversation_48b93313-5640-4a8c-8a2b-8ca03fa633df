package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 高峰期配置类
 * 用于配置各城市的高峰期时间段，支持判断指定时间是否在高峰期
 * 
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("peak_hour.properties")
public class PeakHourConfig {

    /**
     * 全局高峰期时间段配置，格式：HH:mm-HH:mm,HH:mm-HH:mm
     * 例如：globalPeakHours=07:00-09:00,17:00-19:00
     */
    private String globalPeakHours;

    /**
     * 特定城市高峰期时间段配置，格式：cityId:HH:mm-HH:mm,HH:mm-HH:mm;cityId:HH:mm-HH:mm
     * 例如：cityPeakHours=1:07:30-09:30,17:30-19:30;2:08:00-10:00,18:00-20:00
     */
    private String cityPeakHours;

    /**
     * 判断指定时间是否在指定城市的高峰期
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @return true-在高峰期，false-不在高峰期
     */
    public boolean isPeakHour(Integer cityId, Date useTime) {
        if (useTime == null) {
            return false;
        }

        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        String currentTime = timeFormat.format(useTime);

        // 先检查城市特定配置
        if (cityId != null && StringUtils.isNotBlank(cityPeakHours)) {
            String citySpecificHours = getCitySpecificPeakHours(cityId);
            if (StringUtils.isNotBlank(citySpecificHours)) {
                return isTimeInPeakHours(currentTime, citySpecificHours);
            }
        }

        // 使用全局配置
        if (StringUtils.isNotBlank(globalPeakHours)) {
            return isTimeInPeakHours(currentTime, globalPeakHours);
        }

        return false;
    }

    /**
     * 获取指定城市的特定高峰期配置
     * 
     * @param cityId 城市ID
     * @return 高峰期时间段配置
     */
    private String getCitySpecificPeakHours(Integer cityId) {
        if (StringUtils.isBlank(cityPeakHours)) {
            return null;
        }

        List<String> cityConfigs = Splitter.on(";")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(cityPeakHours);

        for (String cityConfig : cityConfigs) {
            List<String> parts = Splitter.on(":")
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(cityConfig);
            
            if (parts.size() >= 2 && cityId.toString().equals(parts.get(0))) {
                return cityConfig.substring(cityConfig.indexOf(":") + 1);
            }
        }

        return null;
    }

    /**
     * 判断当前时间是否在高峰期时间段内
     * 
     * @param currentTime 当前时间 HH:mm格式
     * @param peakHours 高峰期配置 HH:mm-HH:mm,HH:mm-HH:mm格式
     * @return true-在高峰期，false-不在高峰期
     */
    private boolean isTimeInPeakHours(String currentTime, String peakHours) {
        if (StringUtils.isBlank(currentTime) || StringUtils.isBlank(peakHours)) {
            return false;
        }

        List<String> timeRanges = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(peakHours);

        for (String timeRange : timeRanges) {
            List<String> range = Splitter.on("-")
                    .trimResults()
                    .omitEmptyStrings()
                    .splitToList(timeRange);
            
            if (range.size() == 2) {
                String startTime = range.get(0);
                String endTime = range.get(1);
                
                if (isTimeInRange(currentTime, startTime, endTime)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断时间是否在指定范围内
     * 
     * @param currentTime 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return true-在范围内，false-不在范围内
     */
    private boolean isTimeInRange(String currentTime, String startTime, String endTime) {
        try {
            int current = timeToMinutes(currentTime);
            int start = timeToMinutes(startTime);
            int end = timeToMinutes(endTime);
            
            // 处理跨天的情况
            if (start <= end) {
                return current >= start && current <= end;
            } else {
                return current >= start || current <= end;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将时间字符串转换为分钟数
     * 
     * @param time HH:mm格式的时间
     * @return 分钟数
     */
    private int timeToMinutes(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return hours * 60 + minutes;
    }
}
