package com.ctrip.dcs.dsp.delay.util;

import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 路径监控工具类
 * 封装Cat.logTags监控逻辑，支持10个维度的监控指标
 * 
 * <AUTHOR>
 */
public class RouteMonitorUtil {

    private static final Logger logger = LoggerFactory.getLogger(RouteMonitorUtil.class);

    // 监控场景常量
    public static final String SCENARIO_ROUTE_COMPARISON = "route_comparison";
    public static final String SCENARIO_ROUTE_CACHE_HIT = "route_cache_hit";
    public static final String SCENARIO_ROUTE_API_SWITCH = "route_api_switch";
    public static final String SCENARIO_DELAYED_ROUTE_ESTIMATE = "delayed_route_estimate";

    /**
     * 记录路径比较监控指标
     * 
     * @param cityId 城市ID
     * @param departureTime 出发时间
     * @param callTime 调用时间
     * @param startPoint 起点
     * @param endPoint 终点
     * @param realTimeDuration 实时时长
     * @param futureDuration 未来时长
     * @param realTimeFutureDiff 实时调用与未来diff的时长
     * @param cacheHit 是否命中缓存
     */
    public static void logRouteComparison(Integer cityId, Date departureTime, Date callTime,
                                        String startPoint, String endPoint,
                                        Double realTimeDuration, Double futureDuration,
                                        Double realTimeFutureDiff, Boolean cacheHit) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("cityId", String.valueOf(cityId));
            tags.put("departureTime", formatTime(departureTime));
            tags.put("callTime", formatTime(callTime));
            tags.put("startPoint", startPoint);
            tags.put("endPoint", endPoint);
            tags.put("realTimeDuration", String.valueOf(realTimeDuration));
            tags.put("futureDuration", String.valueOf(futureDuration));
            tags.put("realTimeFutureDiff", String.valueOf(realTimeFutureDiff));
            tags.put("cacheHit", String.valueOf(cacheHit));

            logger.info(SCENARIO_ROUTE_COMPARISON, JsonUtil.toJson(tags));
            Cat.logTags(SCENARIO_ROUTE_COMPARISON, tags, null);
        } catch (Exception e) {
            logger.error("RouteMonitorUtil.logRouteComparison error", e);
        }
    }

    /**
     * 记录延迟路径预估监控指标
     * 
     * @param cityId 城市ID
     * @param departureTime 出发时间
     * @param callTime 调用时间
     * @param startPoint 起点
     * @param endPoint 终点
     * @param actualDuration 实际时长
     * @param previousFutureDuration 之前的未来预估时长
     * @param actualFutureDiff 延迟消息中的实际调用结果与之前的未来diff时长
     * @param cacheHit 是否命中缓存
     */
    public static void logDelayedRouteEstimate(Integer cityId, Date departureTime, Date callTime,
                                             String startPoint, String endPoint,
                                             Double actualDuration, Double previousFutureDuration,
                                             Double actualFutureDiff, Boolean cacheHit) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("cityId", String.valueOf(cityId));
            tags.put("departureTime", formatTime(departureTime));
            tags.put("callTime", formatTime(callTime));
            tags.put("startPoint", startPoint);
            tags.put("endPoint", endPoint);
            tags.put("actualDuration", String.valueOf(actualDuration));
            tags.put("previousFutureDuration", String.valueOf(previousFutureDuration));
            tags.put("actualFutureDiff", String.valueOf(actualFutureDiff));
            tags.put("cacheHit", String.valueOf(cacheHit));

            logger.info(SCENARIO_DELAYED_ROUTE_ESTIMATE, JsonUtil.toJson(tags));
            Cat.logTags(SCENARIO_DELAYED_ROUTE_ESTIMATE, tags, null);
        } catch (Exception e) {
            logger.error("RouteMonitorUtil.logDelayedRouteEstimate error", e);
        }
    }

    /**
     * 记录缓存命中监控指标
     * 
     * @param cityId 城市ID
     * @param cacheKey 缓存key
     * @param cacheHit 是否命中缓存
     * @param apiType 接口类型（old/new）
     */
    public static void logCacheHit(Integer cityId, String cacheKey, Boolean cacheHit, String apiType) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("cityId", String.valueOf(cityId));
            tags.put("cacheKey", cacheKey);
            tags.put("cacheHit", String.valueOf(cacheHit));
            tags.put("apiType", apiType);

            logger.info(SCENARIO_ROUTE_CACHE_HIT, JsonUtil.toJson(tags));
            Cat.logTags(SCENARIO_ROUTE_CACHE_HIT, tags, null);
        } catch (Exception e) {
            logger.error("RouteMonitorUtil.logCacheHit error", e);
        }
    }

    /**
     * 记录接口切换监控指标
     * 
     * @param cityId 城市ID
     * @param isTop20City 是否top20城市
     * @param isPeakHour 是否高峰期
     * @param switchMode 切换模式（direct/async）
     * @param apiUsed 实际使用的接口（old/new）
     */
    public static void logApiSwitch(Integer cityId, Boolean isTop20City, Boolean isPeakHour,
                                  String switchMode, String apiUsed) {
        try {
            Map<String, String> tags = new HashMap<>();
            tags.put("cityId", String.valueOf(cityId));
            tags.put("isTop20City", String.valueOf(isTop20City));
            tags.put("isPeakHour", String.valueOf(isPeakHour));
            tags.put("switchMode", switchMode);
            tags.put("apiUsed", apiUsed);

            logger.info(SCENARIO_ROUTE_API_SWITCH, JsonUtil.toJson(tags));
            Cat.logTags(SCENARIO_ROUTE_API_SWITCH, tags, null);
        } catch (Exception e) {
            logger.error("RouteMonitorUtil.logApiSwitch error", e);
        }
    }

    /**
     * 从Position对象提取起点信息
     * 
     * @param position 位置对象
     * @return 起点字符串
     */
    public static String extractStartPoint(Position position) {
        if (position == null) {
            return "unknown";
        }
        return String.format("%.6f,%.6f", position.getFromLatitude(), position.getFromLongitude());
    }

    /**
     * 从Position对象提取终点信息
     * 
     * @param position 位置对象
     * @return 终点字符串
     */
    public static String extractEndPoint(Position position) {
        if (position == null) {
            return "unknown";
        }
        return String.format("%.6f,%.6f", position.getToLatitude(), position.getToLongitude());
    }

    /**
     * 格式化时间
     * 
     * @param date 时间
     * @return 格式化后的时间字符串
     */
    private static String formatTime(Date date) {
        if (date == null) {
            return "unknown";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}
