package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.service.ConflictService;
import com.ctrip.dcs.dsp.delay.service.DispatchService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DispatchServiceImpl implements DispatchService {

    private static final Logger logger = LoggerFactory.getLogger(DispatchServiceImpl.class);

    @Autowired
    private ConflictService conflictService;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public DispatchResult dispatch(DelayDspContext context) {
        logger.info("DispatchServiceImpl.dispatch", "context: {}", context.toString());
        List<DelayDspOrder> orders = context.getOrders();
        List<DriverAggregation> driverAggregations = context.getDriverAggregations();
        if (CollectionUtils.isEmpty(driverAggregations)) {
            logger.info("DispatchServiceImpl.dispatch", "driver is empty! task id: {}", context.getDelayDspTask().getTaskId());
            return new DispatchResult(orders);
        }
        List<DispatchResultDetail> details = Lists.newArrayList();
        Iterator<DriverAggregation> iterator = driverAggregations.iterator();
        while (iterator.hasNext()) {
            DriverAggregation driverAggregation = iterator.next();
            if (CollectionUtils.isEmpty(orders)) {
                break;
            }
            // 派发
            List<DelayDspOrder> result = dispatch(context, driverAggregation, orders);
            // 构建派单结果
            List<DispatchResultDetail> list = ModelFactory.buildDispatchResultDetails(context, driverAggregation, result, geoGateway);
            details.addAll(list);
            // 剔除已匹配到司机的订单
            orders.removeAll(result);
        }
        if (CollectionUtils.isNotEmpty(orders)) {   // 仍有剩余订单
            List<DispatchResultDetail> list = orders.stream().map(DispatchResultDetail::new).collect(Collectors.toList());
            details.addAll(list);
        }
        DispatchResult result = new DispatchResult();
        result.setDetails(details);
        return result;
    }

    private List<DelayDspOrder> dispatch(DelayDspContext context, DriverAggregation driverAggregation, List<DelayDspOrder> orders) {

        List<DelayDspOrder> list = mergeAndSort(driverAggregation.getOrders(), orders);

        long start = System.currentTimeMillis();
        int[] ints = dp(driverAggregation, list, context);
        long end = System.currentTimeMillis();
        MetricsUtil.recordTime("dispatch.dp.time", end - start);

        List<DelayDspOrder> result = Lists.newArrayList();
        for (int i = 0; i < list.size(); i++) {
            if (ints[i] == YesOrNo.YES.getCode()) {
                result.add(list.get(i));
            }
        }
        return result;
    }

    private int[] dp(DriverAggregation driverAggregation, List<DelayDspOrder> orders, DelayDspContext context) {
        int n = orders.size();
        int l = context.getConfig().getIncomeStandardLine();
        DelayDspTask task = context.getDelayDspTask();
        double[] dp = new double[n + 1];    // 服务完对应订单所能产生的最大收益
        int[][] matrix = new int[n][n];
        for (int i = 1; i <= n; i++) {
            try {
                DelayDspOrder order = orders.get(i - 1);
                if (!order.hasTaken() && task.outOfTime(order.getSysExpectBookTime())) { // 订单预估用车时间超出了任务时间范围
                    continue;
                }
                // 初始化收益
                dp[i] = init(driverAggregation, order, context);
                matrix[i - 1][i - 1] = 1;
                for (int j = i - 1; j > 0; j--) {
                    DelayDspOrder prev = orders.get(j - 1);
                    if (!prev.hasTaken() && task.outOfTime(prev.getSysExpectBookTime())) { // 订单预估用车时间超出了任务时间范围
                        continue;
                    }
                    CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, prev, order);
                    if (!checkCode.isConflict() || (order.hasTaken() && prev.hasTaken())) {  // 待派订单i与订单j不冲突
                        // 接了j订单之后再接i订单的最大收益
                        Route route = geoGateway.queryRoute(task.getTaskId(), new Position(prev, order));
                        double v = dp[j] + order.calculateProfit(route);
                        if (dp[i] < v) {
                            dp[i] = v;
                            System.arraycopy(matrix[j - 1], 0, matrix[i - 1], 0, i - 1);
                        }
                    }
                    if (prev.hasTaken()) {  // 订单j是司机已接订单，则没必要在向前遍历
                        if (checkCode.isConflict() && !order.hasTaken()) {        // 待派订单i与司机已接订单j冲突
                            dp[i] = 0.0;
                            matrix[i - 1] = new int[n];
                        }
                        break;
                    }
                }
                if (dp[i] <= 0) {   // 订单i不能被司机接起，赋值成最小值，防止后向单又与i相连
                    dp[i] = Integer.MIN_VALUE;
                    matrix[i - 1] = new int[n];
                }
            } catch (Exception e) {
                dp[i] = Integer.MIN_VALUE;
                matrix[i - 1] = new int[n];
                logger.error("dp error", e);
                MetricsUtil.recordValue("dp.error", 1);
            }
        }
        double best = 0.0;
        int x = -1;
        for (int i = n; i > 0; i--) {
            // 1、当前结果与最优结果都达到收益标准，则取较小的
            // 2、当前结果优于最优，并且没有达到收益标准
            if ((dp[i] >= l && dp[i] < best) || (dp[i] > best && best < l)) {
                List<DelayDspOrder> list = Lists.newArrayList();
                boolean flag = false;
                for (int j = 0; j < matrix[i - 1].length; j++) {
                    DelayDspOrder order = orders.get(j);
                    if (matrix[i - 1][j] == YesOrNo.YES.getCode()) {
                        list.add(order);
                        continue;
                    }
                    if (order.hasTaken()) { // 司机已接订单不在派单组合中，则丢弃这个结果
                        flag = true;
                        logger.info("taken order not in result", "driver:{}, orderId:{}", driverAggregation.getDriver().getDriverId(), order.getOrderId());
                        break;
                    }
                }
                if (flag) { // 司机已接订单不在派单组合中，则丢弃这个结果
                    continue;
                }
                // 检查派单结果是否满足要求
                CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, list);
                if (checkCode.isConflict()) {
                    continue;
                }
                best = dp[i];
                x = i;
            } else {
                logger.info("dp best filter", "driverId:{}, i:{}, l:{}, best:{}", driverAggregation.getDriver().getDriverId(), i, l, best);
            }
        }
        log(driverAggregation, orders, dp, matrix, x);
        return x == -1 ? new int[n] : matrix[x - 1];
    }

    private void log(DriverAggregation driverAggregation, List<DelayDspOrder> orders, double[] dp, int[][] matrix, int x) {
        try {
            List<String> ids = orders.stream().map(DelayDspOrder::getOrderId).collect(Collectors.toList());
            logger.info("dp result", "driver:{}, orders:{}, dp:{}, matrix:{}, res:{}", driverAggregation.getDriver().getDriverId(), ids, JsonUtil.toJson(dp), JsonUtil.toJson(matrix), x);
        } catch (Exception e) {
            logger.error("dp log error!", e);
        }
    }

    private double init(DriverAggregation driverAggregation, DelayDspOrder order, DelayDspContext context) {
        Driver driver = driverAggregation.getDriver();
        DelayDspTask task = context.getDelayDspTask();
        if (!order.hasTaken()) {    // 待派订单不是司机已接订单
            CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, order);
            if (checkCode.isConflict()) {
                // 初始化收益赋值为0，表示订单不能作为首单派给司机，但是订单还是有可能最为非首单派给司机。
                return NumberUtils.DOUBLE_ZERO;
            }
        }
        Route route = geoGateway.queryRoute(task.getTaskId(), new Position(driver, order));
        return order.calculateProfit(route);
    }

    private List<DelayDspOrder> mergeAndSort(List<DelayDspOrder> l1, List<DelayDspOrder> l2) {
        List<DelayDspOrder> list = Lists.newArrayList();
        list.addAll(l1);
        list.addAll(l2);
        list.sort(Comparator.comparing(DelayDspOrder::getPredictServiceStopTime));
        return list;
    }
}
