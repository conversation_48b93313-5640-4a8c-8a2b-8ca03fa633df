package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qconfig.QConfig2;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("delay_dsp_common.properties")
public class DelayDspCommonQConfig {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspCommonQConfig.class);

    private static final Splitter DEFAULT_SPLITTER = Splitter.on(',').trimResults().omitEmptyStrings();

    private Integer virtualDspSwitch;

    private String delayDspValidators;

    private String shortDisDelayDspValidators;

    private String delayTakenValidators;

    private String driverFilters;

    private String delayDspChannel;

    private String notDelayDspChannel;

    private String shortDisOrderCategoryCode;

    private Integer redispatchRoleId;
    private Integer redispatchReasonDetailId;
    private Integer redispatchChgType;
    private String redispatchUserName;
    private String redispatchChgReason;
    private String redispatchReasonId = "127";

    private String userWhite;

    private Integer takenPermitsPerSecond;

    private Integer lbsPermitsPerSecond;

    private Integer newApiPermitsPerSecond;

    private Integer trafficControlServiceProviderId;

    private Integer beforeExecuteTimeHour;

    // 司机-订单检查，初始化检查
    private String driverToOrderChecks;

    // 订单-订单检查，匹配检查
    private String orderToOrderChecks;

    // 订单集合检查，接单前检查
    private String orderToTakenChecks;

    private Integer batchFrozenSize = 100;

    private Integer warnDeadTime = 40;

    private Long delayUnfrozenMillisecond = 8000L;

    private String dispatcherOrderCitySwitch = "";

    private String reachStandardCitySwitch = "";

    private String outPoolTimeChangeCitySwitch = "";

    private Integer outPoolTimeChangeEffectTime = 30 * 60;

    private Integer minutesBeforeDeadTime = 60;

    private Integer delayTaskConfigVersion = 0;

    private Long checkOrderOutMillisecond = 30 * 60 * 1000L;

    private Long checkTaskRunMillisecond = 60 * 60 * 1000L;

    private Long checkMatchTaskRunMillisecond = 3 * 60 * 1000L;

    private Integer outRedispatchSwitch = 0;


    private String driverMatchCheckCityIds;

    private Integer driverMatchMaxSeconds = 15 * 60;

    private String orderProfitCityIds;
    
    private Integer queryDriverTimeOut = 500;
    
    public boolean selfCloudSwitch;

    private Long preOutTimeDelayCheck = 3 * 60 * 1000L;

    private String outPoolReason = null;

    public Set<Long> getDelayDspChannelSet() {
        if (StringUtils.isBlank(delayDspChannel)) {
            return Sets.newHashSet();
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(delayDspChannel);
        return list.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

    public Set<Long> getNotDelayDspChannelSet() {
        if (StringUtils.isBlank(notDelayDspChannel)) {
            return Sets.newHashSet();
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(notDelayDspChannel);
        return list.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

    public Boolean getShortDisOrderCategoryCodeSet(Integer cityId,String categoryCode) {
        try{
            if(StringUtils.isBlank(shortDisOrderCategoryCode)){
                return false;
            }
            Map<String, String> map = JsonUtil.fromJson(shortDisOrderCategoryCode, new TypeReference<Map<String, String>>() {
            });
            String list = map.get(cityId.toString());
            logger.info("getShortDisDelayDspValidators", list);
            if(StringUtils.isBlank(list)){
                return false;
            }
            List<String> categoryCodeList =  DEFAULT_SPLITTER.splitToList(list);

            return categoryCodeList.contains(categoryCode.toLowerCase());
        }catch (Exception ex){
            logger.error("getShortDisDelayDspValidators_error", ex);
        }
        return false;
    }

    public List<String> getDelayDspValidatorsList() {
        if (StringUtils.isBlank(delayDspValidators)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(delayDspValidators);
    }

    public List<String> getShortDisDelayDspValidators() {
        if (StringUtils.isBlank(shortDisDelayDspValidators)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(shortDisDelayDspValidators);
    }

    public List<String> getDelayTakenValidatorsList() {
        if (StringUtils.isBlank(delayTakenValidators)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(delayTakenValidators);
    }

    public List<String> getDriverFilterList() {
        if (StringUtils.isBlank(driverFilters)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(driverFilters);
    }

    public List<String> getUserWhiteList() {
        if (StringUtils.isBlank(userWhite)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(userWhite);
    }

    public List<String> getDriverToOrderCheckList() {
        if (StringUtils.isBlank(driverToOrderChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(driverToOrderChecks);
    }

    public List<String> getOrderToOrderCheckList() {
        if (StringUtils.isBlank(orderToOrderChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(orderToOrderChecks);
    }

    public List<String> getOrderToTakenCheckList() {
        if (StringUtils.isBlank(orderToTakenChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(orderToTakenChecks);
    }

    public Boolean isDispatcherOrderCitySwitch(Integer cityId) {
        if (Objects.isNull(cityId) || cityId == 0 || StringUtils.isBlank(dispatcherOrderCitySwitch)) {
            return false;
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(dispatcherOrderCitySwitch);
        return list.contains(cityId.toString()) || list.contains("all");
    }

    public Boolean isReachStandardCitySwitch(Integer cityId) {
        if (Objects.isNull(cityId) || cityId == 0 || StringUtils.isBlank(reachStandardCitySwitch)) {
            return false;
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(reachStandardCitySwitch);
        return list.contains(cityId.toString()) || list.contains("all");
    }

    public Boolean isOrderProfitCity(Integer cityId) {
        if (StringUtils.isBlank(orderProfitCityIds) || cityId == null) {
            return false;
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(orderProfitCityIds);
        return list.contains(cityId.toString()) || list.contains("all");
    }

    public Boolean isOutPoolTimeChangeCitySwitch(String cityId) {
        if (StringUtils.isBlank(outPoolTimeChangeCitySwitch) || "0".equals(cityId)) {
            return false;
        }
        List<String> list = DEFAULT_SPLITTER.splitToList(outPoolTimeChangeCitySwitch);
        return list.contains(cityId) || list.contains("all");
    }

    public Integer getOutPoolTimeChangeEffectTime() {
        return outPoolTimeChangeEffectTime;
    }

    public Integer getDelayTaskConfigVersion() {
        return delayTaskConfigVersion;
    }

    public Integer getMinutesBeforeDeadTime() {
        return minutesBeforeDeadTime;
    }

    public Integer getWarnDeadTime() {
        return warnDeadTime;
    }


    public String getOutReasonConfig(String index) {
        try{
            if(StringUtils.isBlank(index)){
                return "";
            }
            Map<String, String> map = JsonUtil.fromJson(outPoolReason, new TypeReference<Map<String, String>>() {
            });
            String reason = map.get(index);
            logger.info("DelayDspCommonQConfig_getOutReason", reason);
            return reason;
        }catch (Exception ex){
            logger.error("DelayDspCommonQConfig_getOutReason_error", ex);
            return "";
        }
    }
}
