package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.RouteApiSwitchConfig;
import com.ctrip.dcs.dsp.delay.util.RouteMonitorUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 路径比较服务
 * 用于比较实时调用、未来预估和实际调用结果的差异，并进行监控埋点
 * 
 * <AUTHOR>
 */
@Service
public class RouteComparisonService {

    private static final Logger logger = LoggerFactory.getLogger(RouteComparisonService.class);

    @Autowired
    private RouteApiSwitchConfig routeApiSwitchConfig;

    /**
     * 比较实时调用与未来预估结果
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param positions 位置列表
     * @param realTimeRoutes 实时调用结果
     * @param futureRoutes 未来预估结果
     */
    public void compareRealTimeAndFuture(Integer cityId, Date useTime, List<Position> positions,
                                       List<Route> realTimeRoutes, List<Route> futureRoutes) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            Date callTime = new Date();
            int minSize = Math.min(positions.size(), Math.min(realTimeRoutes.size(), futureRoutes.size()));
            
            for (int i = 0; i < minSize; i++) {
                Position position = positions.get(i);
                Route realTimeRoute = realTimeRoutes.get(i);
                Route futureRoute = futureRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double realTimeDuration = realTimeRoute.getDuration();
                Double futureDuration = futureRoute.getDuration();
                Double diff = Math.abs(realTimeDuration - futureDuration);

                // 记录路径比较监控指标
                RouteMonitorUtil.logRouteComparison(cityId, useTime, callTime,
                        startPoint, endPoint, realTimeDuration, futureDuration, diff, false);

                logger.info("RouteComparisonService.compareRealTimeAndFuture", 
                        "cityId: {}, position: {}->{}, realTime: {}, future: {}, diff: {}",
                        cityId, startPoint, endPoint, realTimeDuration, futureDuration, diff);
            }

        } catch (Exception e) {
            logger.error("RouteComparisonService.compareRealTimeAndFuture error", e);
        }
    }

    /**
     * 比较实际调用与之前预估结果
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param positions 位置列表
     * @param actualRoutes 实际调用结果
     * @param previousRoutes 之前预估结果
     */
    public void compareActualAndPrevious(Integer cityId, Date useTime, List<Position> positions,
                                       List<Route> actualRoutes, List<Route> previousRoutes) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            Date callTime = new Date();
            int minSize = Math.min(positions.size(), Math.min(actualRoutes.size(), previousRoutes.size()));
            
            for (int i = 0; i < minSize; i++) {
                Position position = positions.get(i);
                Route actualRoute = actualRoutes.get(i);
                Route previousRoute = previousRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double actualDuration = actualRoute.getDuration();
                Double previousDuration = previousRoute.getDuration();
                Double diff = Math.abs(actualDuration - previousDuration);

                // 记录延迟路径预估监控指标
                RouteMonitorUtil.logDelayedRouteEstimate(cityId, useTime, callTime,
                        startPoint, endPoint, actualDuration, previousDuration, diff, false);

                logger.info("RouteComparisonService.compareActualAndPrevious", 
                        "cityId: {}, position: {}->{}, actual: {}, previous: {}, diff: {}",
                        cityId, startPoint, endPoint, actualDuration, previousDuration, diff);
            }

        } catch (Exception e) {
            logger.error("RouteComparisonService.compareActualAndPrevious error", e);
        }
    }

    /**
     * 比较三种结果：实时、未来预估、实际调用
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param positions 位置列表
     * @param realTimeRoutes 实时调用结果
     * @param futureRoutes 未来预估结果
     * @param actualRoutes 实际调用结果
     */
    public void compareAllThreeResults(Integer cityId, Date useTime, List<Position> positions,
                                     List<Route> realTimeRoutes, List<Route> futureRoutes, List<Route> actualRoutes) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            Date callTime = new Date();
            int minSize = Math.min(positions.size(), 
                    Math.min(realTimeRoutes.size(), 
                            Math.min(futureRoutes.size(), actualRoutes.size())));
            
            for (int i = 0; i < minSize; i++) {
                Position position = positions.get(i);
                Route realTimeRoute = realTimeRoutes.get(i);
                Route futureRoute = futureRoutes.get(i);
                Route actualRoute = actualRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double realTimeDuration = realTimeRoute.getDuration();
                Double futureDuration = futureRoute.getDuration();
                Double actualDuration = actualRoute.getDuration();
                
                Double realTimeFutureDiff = Math.abs(realTimeDuration - futureDuration);
                Double actualFutureDiff = Math.abs(actualDuration - futureDuration);
                Double realTimeActualDiff = Math.abs(realTimeDuration - actualDuration);

                // 记录综合比较监控指标
                logger.info("RouteComparisonService.compareAllThreeResults", 
                        "cityId: {}, position: {}->{}, realTime: {}, future: {}, actual: {}, " +
                        "realTimeFutureDiff: {}, actualFutureDiff: {}, realTimeActualDiff: {}",
                        cityId, startPoint, endPoint, realTimeDuration, futureDuration, actualDuration,
                        realTimeFutureDiff, actualFutureDiff, realTimeActualDiff);

                // 分别记录不同类型的比较
                RouteMonitorUtil.logRouteComparison(cityId, useTime, callTime,
                        startPoint, endPoint, realTimeDuration, futureDuration, realTimeFutureDiff, false);
                
                RouteMonitorUtil.logDelayedRouteEstimate(cityId, useTime, callTime,
                        startPoint, endPoint, actualDuration, futureDuration, actualFutureDiff, false);
            }

        } catch (Exception e) {
            logger.error("RouteComparisonService.compareAllThreeResults error", e);
        }
    }

    /**
     * 计算路径结果的统计信息
     * 
     * @param routes 路径列表
     * @return 统计信息
     */
    public RouteStatistics calculateStatistics(List<Route> routes) {
        if (CollectionUtils.isEmpty(routes)) {
            return new RouteStatistics();
        }

        double totalDistance = 0.0;
        double totalDuration = 0.0;
        double maxDistance = Double.MIN_VALUE;
        double minDistance = Double.MAX_VALUE;
        double maxDuration = Double.MIN_VALUE;
        double minDuration = Double.MAX_VALUE;

        for (Route route : routes) {
            if (route.getDistance() != null) {
                totalDistance += route.getDistance();
                maxDistance = Math.max(maxDistance, route.getDistance());
                minDistance = Math.min(minDistance, route.getDistance());
            }
            if (route.getDuration() != null) {
                totalDuration += route.getDuration();
                maxDuration = Math.max(maxDuration, route.getDuration());
                minDuration = Math.min(minDuration, route.getDuration());
            }
        }

        RouteStatistics statistics = new RouteStatistics();
        statistics.setCount(routes.size());
        statistics.setTotalDistance(totalDistance);
        statistics.setTotalDuration(totalDuration);
        statistics.setAvgDistance(totalDistance / routes.size());
        statistics.setAvgDuration(totalDuration / routes.size());
        statistics.setMaxDistance(maxDistance == Double.MIN_VALUE ? 0.0 : maxDistance);
        statistics.setMinDistance(minDistance == Double.MAX_VALUE ? 0.0 : minDistance);
        statistics.setMaxDuration(maxDuration == Double.MIN_VALUE ? 0.0 : maxDuration);
        statistics.setMinDuration(minDuration == Double.MAX_VALUE ? 0.0 : minDuration);

        return statistics;
    }

    /**
     * 路径统计信息
     */
    public static class RouteStatistics {
        private int count;
        private double totalDistance;
        private double totalDuration;
        private double avgDistance;
        private double avgDuration;
        private double maxDistance;
        private double minDistance;
        private double maxDuration;
        private double minDuration;

        // Getters and Setters
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
        public double getTotalDistance() { return totalDistance; }
        public void setTotalDistance(double totalDistance) { this.totalDistance = totalDistance; }
        public double getTotalDuration() { return totalDuration; }
        public void setTotalDuration(double totalDuration) { this.totalDuration = totalDuration; }
        public double getAvgDistance() { return avgDistance; }
        public void setAvgDistance(double avgDistance) { this.avgDistance = avgDistance; }
        public double getAvgDuration() { return avgDuration; }
        public void setAvgDuration(double avgDuration) { this.avgDuration = avgDuration; }
        public double getMaxDistance() { return maxDistance; }
        public void setMaxDistance(double maxDistance) { this.maxDistance = maxDistance; }
        public double getMinDistance() { return minDistance; }
        public void setMinDistance(double minDistance) { this.minDistance = minDistance; }
        public double getMaxDuration() { return maxDuration; }
        public void setMaxDuration(double maxDuration) { this.maxDuration = maxDuration; }
        public double getMinDuration() { return minDuration; }
        public void setMinDuration(double minDuration) { this.minDuration = minDuration; }
    }
}
