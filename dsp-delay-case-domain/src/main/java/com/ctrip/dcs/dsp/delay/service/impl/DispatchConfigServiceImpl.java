package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspSubSkuKey;
import com.ctrip.dcs.dsp.delay.carconfig.key.DriverProfitDayBaselineKey;
import com.ctrip.dcs.dsp.delay.carconfig.key.HeadTailLimitKey;
import com.ctrip.dcs.dsp.delay.carconfig.key.LBSBufferKey;
import com.ctrip.dcs.dsp.delay.carconfig.key.OrderMileageKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.LBSBufferValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.OrderMileageValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DispatchConfigServiceImpl implements DispatchConfigService {

    private static final Logger logger = LoggerFactory.getLogger(DispatchConfigServiceImpl.class);

    @Autowired
    private CarConfigGateway carConfigGateway;

    @Override
    public DispatcherConfig buildDispatcherConfig(DelayDspTask task) {
        // 收益基线配置
        DriverProfitDayBaselineValue driverProfitDayBaselineValue = queryDriverProfitDayBaselineValue(task);
        logger.info("DispatchConfigServiceInfo", JsonUtil.toJson(driverProfitDayBaselineValue));
        // 里程价值配置
        List<OrderMileageValue> orderMileageValues = queryOrderMileageValue(task);
        logger.info("DispatchConfigServiceInfo", JsonUtil.toJson(orderMileageValues));
        // lbs buffer配置
        List<LBSBufferValue> lbsBufferValues = queryLbsBufferValue(task);
        logger.info("DispatchConfigServiceInfo", JsonUtil.toJson(lbsBufferValues));
        // 首尾配置
        HeadTailLimitValue headTailLimitValue = queryHeadTailLimitConfig(task);
        logger.info("DispatchConfigServiceInfo", JsonUtil.toJson(headTailLimitValue));
        return new DispatcherConfig(orderMileageValues, driverProfitDayBaselineValue, lbsBufferValues, headTailLimitValue);
    }

    @Override
    public DelayDspSubSkuValue matchDelayDspSubSkuConfig(SupplyOrder order) {
        return matchDelayDspSubSkuConfig(order.getCityCode());
    }
    
    @Override
    public DelayDspSubSkuValue matchDelayDspSubSkuConfig(String cityCode) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.DELAY_DSP_SUB_SKU.getCode(), CarConfigCode.DELAY_DSP_SUB_SKU.getVersion());
        List<DelayDspSubSkuValue> list = carConfig.values(
                new TypeReference<DelayDspSubSkuValue>(){},
                new DelayDspSubSkuKey(cityCode),
                new DelayDspSubSkuKey(Key.ALL_CITY)
        );
        return CollectionUtils.isEmpty(list) ? DelayDspSubSkuValue.DEFAULT : list.get(0);
    }
    
    private List<OrderMileageValue> queryOrderMileageValue(DelayDspTask task) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.ORDER_MILEAGE_VALUE.getCode(), CarConfigCode.ORDER_MILEAGE_VALUE.getVersion());
        return carConfig.values(
                new TypeReference<List<OrderMileageValue>>(){},
                new OrderMileageKey(task.getCityCode(), task.getCarTypeId()),
                new OrderMileageKey(task.getCityCode(), Key.ALL_CAR_TYPE),
                new OrderMileageKey(Key.ALL_CITY, Key.ALL_CAR_TYPE)
        );
    }

    private DriverProfitDayBaselineValue queryDriverProfitDayBaselineValue(DelayDspTask task) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.DRIVER_PROFIT_DAY_BASELINE.getCode(), CarConfigCode.DRIVER_PROFIT_DAY_BASELINE.getVersion());
        List<DriverProfitDayBaselineValue> values = carConfig.values(
                new TypeReference<DriverProfitDayBaselineValue>(){},
                new DriverProfitDayBaselineKey(task.getCityCode(), task.getCarTypeId()),
                new DriverProfitDayBaselineKey(task.getCityCode(), Key.ALL_CAR_TYPE),
                new DriverProfitDayBaselineKey(Key.ALL_CITY, Key.ALL_CAR_TYPE)
        );
        return CollectionUtils.isEmpty(values) ? DriverProfitDayBaselineValue.DEFAULT : values.get(0);
    }

    private List<LBSBufferValue> queryLbsBufferValue(DelayDspTask task) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.LBS_BUFFER_CONFIG.getCode(), CarConfigCode.LBS_BUFFER_CONFIG.getVersion());
        return carConfig.values(
                new TypeReference<List<LBSBufferValue>>(){},
                new LBSBufferKey(task.getCityCode()),
                new LBSBufferKey(Key.ALL_CITY)
        );
    }

    private HeadTailLimitValue queryHeadTailLimitConfig(DelayDspTask task) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.HEAD_TAIL_LIMIT.getCode(), CarConfigCode.HEAD_TAIL_LIMIT.getVersion());
        List<HeadTailLimitValue> values = carConfig.values(
                new TypeReference<HeadTailLimitValue>() {
                },
                new HeadTailLimitKey(task.getCityCode(), task.getCarTypeId()),
                new HeadTailLimitKey(task.getCityCode(), Key.ALL_CAR_TYPE),
                new HeadTailLimitKey(Key.ALL_CITY, Key.ALL_CAR_TYPE)
        );
        return CollectionUtils.isEmpty(values) ? HeadTailLimitValue.DEFAULT : values.get(0);
    }
}
