package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.lock.DistributedLock;
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigInfoDTO;
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DSPScheduleMatchValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig.DELAY_TASK_CONFIG_TYPE_DP;
import static com.ctrip.dcs.dsp.delay.util.DateUtil.DATE_FMT;

/**
 * <AUTHOR>
 */
@Component
public class DelayTaskServiceImpl implements DelayTaskService {

    private static final Logger logger = LoggerFactory.getLogger(DelayTaskServiceImpl.class);

    private static final String TRY_ACQUIRE_KEY_FORMAT = "DELAY_TASK_CREATE_%s_%s_%s_%s_%s_%s";

    @Autowired
    private DispatchConfigService dispatchConfigService;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private DelayTaskConfig delayTaskConfig;

    @Autowired
    private DistributedLock distributedLock;

    @Autowired
    private DSPScheduleMatchValueConfig dspScheduleMatchValueConfig;


    public Integer getMatchTaskTime(DelayDspTask task) {
        try {
            if(Objects.isNull(task.getExecuteTime())){
                return 0;
            }
            logger.info("DelayTaskServiceImpl_getMatchTaskTime_task", JsonUtil.toJson(task));
            DSPScheduleMatchValueVO dspScheduleMatchValue = dspScheduleMatchValueConfig.getValue(task.getCityId().toString(), task.getCarTypeId());
            logger.info("DelayTaskServiceImpl.getMatchTaskTime_values", JsonUtil.toJson(dspScheduleMatchValue));
            if(Objects.isNull(dspScheduleMatchValue)){
                return 0;
            }
            Date matchTime = task.getMatchTime();
            if(matchTime.after(task.getExecuteTime())){
                MetricsUtil.recordValue("DelayTaskServiceImpl.getMatchTaskTime.matchTime.after",1L);
                return 0;
            }
            Date executeTime = task.getExecuteTime();
            Date dividingLine = DateUtil.addHours(executeTime, - dspScheduleMatchValue.getTimeRange());
            logger.info("DelayTaskServiceImpl_getMatchTaskTime_dividingLine", JsonUtil.toJson(dividingLine));
            if(matchTime.before(dividingLine)){ //执行时间在分界线左边
               //下次定时匹配任务执行时间仍在分界线左边
                if(DateUtil.minutesDiff(matchTime, dividingLine) >= Double.parseDouble(dspScheduleMatchValue.getMaxInterval().toString())){
                    //如果时间在分界线时时间以外，并且距离分界线大于等于频率，下次执行时间是当前时间+maxInterval
                    Integer maxInterval = dspScheduleMatchValue.getMaxInterval();
                    logger.info("DelayTaskServiceImpl_getMatchTaskTime_maxInterval", JsonUtil.toJson(maxInterval));
                    return maxInterval;
                }else{
                    Integer minInterval = dspScheduleMatchValue.getMinInterval();
                    //左边任务不执行了，下次执行时间在分界线右边
                    Integer interval = calculateNextExecuteTime(minInterval, dividingLine, matchTime);
                    logger.info("DelayTaskServiceImpl_getMatchTaskTime_interval", JsonUtil.toJson(interval));
                    return interval;
                }
            }else{
                if(DateUtil.minutesDiff(matchTime, executeTime) > Double.parseDouble(dspScheduleMatchValue.getMinInterval().toString())){
                    //当前时间距离出池时间>minInterval,下次执行时间是当前时间+minInterval
                    Integer minInterval2 = dspScheduleMatchValue.getMinInterval();
                    logger.info("DelayTaskServiceImpl_getMatchTaskTime_minInterval2", JsonUtil.toJson(minInterval2));
                    return minInterval2;
                }else{
                    //当前时间距离出池时间<=minInterval,不再执行定时匹配任务
                    return 0;
                }
            }
        } catch (Exception e) {
            MetricsUtil.recordValue("DelayTaskServiceImpl.getMatchTaskTime.error",1L);
            logger.error("DelayTaskServiceImpl_getMatchTaskTime_error", e);
            return null;
        }
    }

    private Integer calculateNextExecuteTime(Integer minInterval, Date dividingLine, Date matchTime) {
        Date realMatchTime = DateUtil.addMinutes(matchTime, minInterval);
        int interval = minInterval;
        while (realMatchTime.before(dividingLine)) {
            realMatchTime = DateUtil.addMinutes(realMatchTime, minInterval);
            interval += minInterval;
        }
        return interval;
    }

    @Override
    public DelayDspTask queryOrCreate(SupplyOrder order) {
        if (!order.isTaken()) {
            // 订单未接单
            return queryOrCreate(order.getCityId(), order.getCityCode(), order.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
        }
        // 若订单已被司机接起，则使用司机的城市和车型来匹配延后派任务
        Driver driver = driverGateway.query(order.getDriverId());
        if (Objects.isNull(driver)) {
            return null;
        }
        return queryOrCreate(driver.getCityId(), driver.getCityCode(), driver.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
    }
    
    @Override
    public DelayDspTask queryOrCreate(Driver driver, Date sysExpectBookTime,Integer shortDisOrder) {
        return queryOrCreate(driver.getCityId(), driver.getCityCode(), driver.getCarTypeId(), sysExpectBookTime,shortDisOrder);
    }
    
    @Override
    public DelayDspTask buildTaskTemp(SupplyOrder order) {
        if (!order.isTaken()) {
            // 订单未接单
            return buildTask(order.getCityId(), order.getCityCode(), order.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
        }
        // 若订单已被司机接起，则使用司机的城市和车型来匹配延后派任务
        Driver driver = driverGateway.query(order.getDriverId());
        if (Objects.isNull(driver)) {
            return null;
        }
        return buildTask(driver.getCityId(), driver.getCityCode(), driver.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
    }

    @Override
    public void updateTaskInfo(DelayDspTask delayDspTask) {
        delayDspTaskRepository.updateTaskInfo(delayDspTask);
    }

    @Override
    public List<DelayDspConfigResInfoDTO> queryDelayDspConfigList(List<DelayDspConfigInfoDTO> delayDspConfigInfoList) {
        List<DelayDspConfigResInfoDTO> result = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(delayDspConfigInfoList)){
            for (DelayDspConfigInfoDTO delayDspConfigInfoDTO : delayDspConfigInfoList) {
                DelayDspTaskValue value = delayTaskConfig.getAnyType(delayDspConfigInfoDTO.getCityId().intValue(), delayDspConfigInfoDTO.getCarTypeId(), delayDspConfigInfoDTO.getSysExpectBookTime(), delayDspConfigInfoDTO.getTaskTypes());
                if (Objects.isNull(value)) {
                    logger.info("queryDelayDspConfigList", "not match delay dsp task config, cityId:{},carTypeId:{},datetime:{}", delayDspConfigInfoDTO.getCityId(), delayDspConfigInfoDTO.getCarTypeId(), JacksonUtil.serialize(delayDspConfigInfoDTO.getSysExpectBookTime()));
                    continue;
                }
                DelayDspTask task = ModelFactory.buildDelayDspTask(null, delayDspConfigInfoDTO.getCityId().intValue(), delayDspConfigInfoDTO.getCarTypeId(), delayDspConfigInfoDTO.getSysExpectBookTime(), value);
                buildMatchInfo(task, value);
                DelayDspConfigResInfoDTO resDTO = new DelayDspConfigResInfoDTO();
                resDTO.setCityId(delayDspConfigInfoDTO.getCityId());
                resDTO.setCarTypeId(delayDspConfigInfoDTO.getCarTypeId());
                resDTO.setSysExpectBookTime(DateUtil.formatDate(delayDspConfigInfoDTO.getSysExpectBookTime(),DATE_FMT));
                resDTO.setTaskDeadTime(DateUtil.formatDate(task.getExecuteTimeDeadline(),DATE_FMT));
                resDTO.setTaskEndTime(DateUtil.formatDate(task.getEndTime(),DATE_FMT));
                result.add(resDTO);
            }
        }
        return result;
    }

    private DelayDspTask queryOrCreate(Integer cityId, String cityCode, Integer carTypeId, Date sysExpectBookTime,Integer shortDisOrder) {
        DelayDspTaskValue value = delayTaskConfig.get(cityId, carTypeId, sysExpectBookTime,shortDisOrder);
        if (Objects.isNull(value)) {
            logger.info("DelayTaskServiceImpl", "not match delay dsp task config, cityCode:{},carTypeId:{},datetime:{}", cityCode, carTypeId, JsonUtil.toJson(sysExpectBookTime));
            return null;
        }
        DelayDspTask task = ModelFactory.buildDelayDspTask(cityCode, cityId, carTypeId, sysExpectBookTime, value);
        buildMatchInfo(task, value);
        DelayDspTask result = null;
        if (!DelayDspTaskType.isSD(task.getTaskType())) {
            result = delayDspTaskRepository.query(task);
        }
        if (Objects.nonNull(result)) {
            return result;
        }
        // 创建新的任务
        return create(task);
    }

    private void buildMatchInfo(DelayDspTask task, DelayDspTaskValue value) {
        task.setConfigBeginTime(value.getBegin());
        task.setConfigEndTime(value.getEnd());
        task.setMatchTime( new Date());
        task.setMatchStatus(MatchTaskStatus.END.getCode());
        task.setTaskVersion(0);
        //新增的类型 是什么逻辑？
        //KM任务不需要匹配
        if(!task.getTaskType().equals(DelayDspTaskType.DP.name())){
            return;
        }
        Integer matchTaskTime = this.getMatchTaskTime(task);
        if(Objects.nonNull(matchTaskTime) && matchTaskTime > 0) {
            task.setMatchStatus(MatchTaskStatus.START.getCode());
            task.setTaskVersion(1);
            //任务刚刚创建就匹配没有意义，所以要等到下个周期再匹配
            task.setMatchTime(DateUtil.addMinutes(task.getMatchTime(), matchTaskTime));
        }
    }


    private DelayDspTask buildTask(Integer cityId, String cityCode, Integer carTypeId, Date sysExpectBookTime,Integer shortDisOrder) {
        DelayDspTaskValue value = delayTaskConfig.get(cityId, carTypeId, sysExpectBookTime,shortDisOrder);
        logger.info("DelayTaskServiceImpl_query", JsonUtil.toJson(value));
        if (Objects.isNull(value)) {
            return null;
        }
        DelayDspTask task = ModelFactory.buildDelayDspTask(cityCode, cityId, carTypeId, sysExpectBookTime, value);
        buildMatchInfo(task, value);
        logger.info("DelayTaskServiceImpl_buildDelayDspTask", JsonUtil.toJson(task));
        return task;
    }
    /**
     * 创建延后派任务
     * @param task
     * @return
     */
    private DelayDspTask create(DelayDspTask task) {
        // 防止并发创建任务
        if (!tryAcquire(task)) {
            return null;
        }
        delayDspTaskRepository.save(task);
        if (task.getTaskId() == null || task.getTaskId() == 0L) {
            return null;
        }
        return task;
    }

    /**
     * 并发创建幂等
     * @param task
     * @return
     */
    private boolean tryAcquire(DelayDspTask task) {
        String key = String.format(
                TRY_ACQUIRE_KEY_FORMAT,
                task.getCityId(),
                task.getCarTypeId(),
                DateUtil.formatDate(task.getBeginTime(), DATE_FMT),
                DateUtil.formatDate(task.getEndTime(), DATE_FMT),
                DateUtil.formatDate(task.getExecuteTime(), DATE_FMT),
                DateUtil.formatDate(task.getExecuteTimeDeadline(), DATE_FMT)
        );
        boolean ok = distributedLock.tryAcquire(key, "ok", 10);
        logger.info("DelayTaskServiceImpl", "create task try acquire, key:{}, value:{}", key, ok);
        return ok;
    }
}
