package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.core.KM;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.factory.KMFactory;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.AvailableDriver;
import com.ctrip.dcs.dsp.delay.service.DispatchService;
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService;
import com.ctrip.dcs.dsp.delay.service.QueryAvailableDriversService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class KMDispatcherTaskServiceImpl implements DispatchTaskService {

    private static final Logger logger = LoggerFactory.getLogger(KMDispatcherTaskServiceImpl.class);

    @Autowired
    private QueryAvailableDriversService queryAvailableDriversService;

    @Autowired
    private KMFactory kmFactory;

    @PostConstruct
    public void register() {
        DispatcherManager.register(DelayDspTaskType.KM, this);
    }

    @Override
    public DispatchResult dispatch(DelayDspContext context) {
        List<DelayDspOrder> orders = context.getOrders();
        AvailableDriver recommend = queryAvailableDriversService.query(orders);
        if (Objects.isNull(recommend) || CollectionUtils.isEmpty(recommend.getDetails())) {
            logger.info("KMDispatcherService.dispatch", "driver is empty! task id: {}", context.getDelayDspTask().getTaskId());
            return new DispatchResult(orders);
        }
        Map<String, DelayDspOrder> orderMap = orders.stream().collect(Collectors.toMap(DelayDspOrder::getOrderId, o -> o, (o1, o2) -> o2));
        Map<Long, Driver> driverMap = recommend.drivers();
        Table<Long /*driverId*/, String /*orderId*/, Double /*value*/> table = recommend.table();
        KM km = kmFactory.create(orderMap, driverMap, table);
        int[] match = km.execute();
        // 构建派单结果
        List<DispatchResultDetail> list = match(orderMap, driverMap, recommend, match);
        DispatchResult result = new DispatchResult();
        result.setDetails(list);
        return result;
    }

    public List<DispatchResultDetail> match(Map<String, DelayDspOrder> orderMap, Map<Long, Driver> driverMap, AvailableDriver recommend, int[] match) {
        List<DispatchResultDetail> result = Lists.newArrayList();
        List<Long> driverIds = Lists.newArrayList(driverMap.keySet());
        List<String> orderIds = Lists.newArrayList(orderMap.keySet());
        Set<String> matchOrderIds = Sets.newHashSet();
        for (int i = 0; i < orderIds.size(); i++) {
            for(int j = 0; j < match.length; j++){
                if(i == match[j] && driverIds.size() > j){
                    String orderId = orderIds.get(i);
                    Long driverId = driverIds.get(j);
                    if (recommend.isAvailable(orderId, driverId)) {
                        DispatchResultDetail detail = new DispatchResultDetail(driverMap.get(driverId), orderMap.get(orderId));
                        result.add(detail);
                        matchOrderIds.add(orderId);
                    }
                }
            }
        }
        for (String orderId : orderIds) {
            if (!matchOrderIds.contains(orderId)) {
                result.add(new DispatchResultDetail(orderMap.get(orderId)));
            }
        }
        return result;
    }

}
