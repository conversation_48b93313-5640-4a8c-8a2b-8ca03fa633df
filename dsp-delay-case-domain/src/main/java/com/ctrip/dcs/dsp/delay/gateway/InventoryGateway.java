package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.Frozen;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InventoryGateway {

    /**
     * 释放冻结运力
     * @param order
     * @param driver
     */
    void unfrozen(DelayDspOrder order, Driver driver);

    void unfrozen(String orderId, String driverId);

    /**
     * 批量冻结
     * @param list
     */
    List<Frozen> batchFrozen(List<Frozen> list);


    String checkInventoryConflict(List<String> driverIdList, SupplyOrder order);
}
