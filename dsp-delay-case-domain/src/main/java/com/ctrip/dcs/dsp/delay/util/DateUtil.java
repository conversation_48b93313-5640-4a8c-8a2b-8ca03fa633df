package com.ctrip.dcs.dsp.delay.util;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String HOUR_MIN_FMT = "HH:mm";
    
    public static final String HOUR_MIN_SEC_FMT = "HH:mm:ss";

    public static final String HOUR_FMT = "yyyy-MM-dd-HH";

    public static final String DAY_FMT = "yyyy-MM-dd";

    public static final String DATE_FMT = "yyyy-MM-dd HH:mm:ss";
    
    public static final String MONTH_TO_DAY_FMT = "MM-dd";

    /**
     * 时间差
     * @param start
     * @param end
     * @return 秒
     */
    public static double secondsDiff(Date start, Date end) {
        return (end.getTime() - start.getTime()) / 1000D;
    }

    public static double minutesDiff(Date start, Date end) {
        return secondsDiff(start, end) / 60D;
    }

    public static Date parseDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return DateUtils.parseDate(date, "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
            LOGGER.error("parse date error!", e);
        }
        return null;
    }
    
    public static Date parseDate(String date, String pattern) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return DateUtils.parseDate(date, pattern);
        } catch (ParseException e) {
            LOGGER.error("parse date error!", e);
        }
        return null;
    }

    public static Date parseTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return DateUtils.parseDate(date, "HH:mm");
        } catch (ParseException e) {
            LOGGER.error("parse date error!", e);
        }
        return null;
    }

    public static Date addHours(final Date date, final int amount) {
        return DateUtils.addHours(date, amount);
    }

    public static Date addMinutes(final Date date, final int amount) {
        return DateUtils.addMinutes(date, amount);
    }

    public static Date addSeconds(final Date date, final int amount) {
        return DateUtils.addSeconds(date, amount);
    }

    public static Date addDays(final Date date, final int amount) {
        return DateUtils.addDays(date, amount);
    }

    public static String formatDate(Date time, String format) {
        if (time == null) {
            return "";
        }
        return DateFormatUtils.format(time, format);
    }

    public static Date toDate(Calendar calendar) {
        if (Objects.isNull(calendar)) {
            return null;
        }
        return calendar.getTime();
    }

    /**
     * 两个时间段是否有重叠
     * @param sourceBegin
     * @param sourceEnd
     * @param targetBegin
     * @param targetEnd
     * @return true-不重叠，false-重叠
     */
    public static boolean notOverlap(Date sourceBegin, Date sourceEnd, Date targetBegin, Date targetEnd) {
        List<String> source = split(sourceBegin, sourceEnd, 15);
        List<String> target = split(targetBegin, targetEnd, 15);
        source.retainAll(target);
        return source.isEmpty();
    }

    /**
     * 时间划分
     * 根据刻度长度，从开始时间到结束时间划分出若干个时刻
     * @param beginTime
     * @param endTime
     * @param scale 分钟
     * @return
     */
    public static List<String> split(Date beginTime, Date endTime, int scale) {

        List<String> items = Lists.newArrayList();
        Calendar cld = Calendar.getInstance();
        cld.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        int second = cld.get(Calendar.SECOND);
        cld.add(Calendar.SECOND, 0 - second);
        int minute = cld.get(Calendar.MINUTE);
        int deltaMin = minute % scale;
        if (0 != deltaMin) {
            minute -= deltaMin;// decr to a quarter
            cld.set(Calendar.MINUTE, minute);
        }
        // while (cld.before(end)) {
        while (cld.getTimeInMillis() <= end.getTimeInMillis()) {
            items.add(formatDate(cld.getTime(), "yyyy-MM-dd HH:mm"));
            cld.add(Calendar.MINUTE, scale);
        }
        return items;
    }
}
