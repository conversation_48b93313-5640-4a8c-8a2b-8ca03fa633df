package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplyOrderService {

    void redispatch(DelayDspOrder orderId, DelayDspTaskRecord record);

    void taken(DelayDspTaskRecord record);

    void redispatch(DelayDspTaskRecord record);

    List<String> batchFrozen(List<DispatchResultDetail> matchDetails);
}
