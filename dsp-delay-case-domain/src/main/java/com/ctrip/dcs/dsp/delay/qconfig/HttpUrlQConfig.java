package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("http_url.properties")
public class HttpUrlQConfig {

    private String carconfigUrl;

    private String cityInfoUrl;

    private String driverScoreUrl;

    private String orderInfoDetailUrl;

    private String redispatchUrl;

    private String initDspContextUrl;

    private String orderTakenUrl;

    private String relateOrdersUrl;

    private String userChoicePackageServiceUrl;

    private String unfrozenUrl;

    private String batchFrozenUrl;
}
