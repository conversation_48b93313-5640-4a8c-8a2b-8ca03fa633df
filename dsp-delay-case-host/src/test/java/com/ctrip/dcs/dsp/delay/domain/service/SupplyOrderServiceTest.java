//package com.ctrip.dcs.dsp.delay.domain.service;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.gateway.KoranGateway;
//import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
//import com.ctrip.dcs.dsp.delay.model.DispatchResult;
//import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
//import com.ctrip.dcs.dsp.delay.model.Driver;
//import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
//import com.ctrip.dcs.dsp.delay.model.Duid;
//import com.ctrip.dcs.dsp.delay.model.Route;
//import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo;
//import com.ctrip.dcs.dsp.delay.service.SupplyOrderService;
//import com.google.common.collect.Lists;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class SupplyOrderServiceTest {
//
//    @Autowired
//    private SupplyOrderService supplyOrderService;
//
//    @Autowired
//    private KoranGateway koranGateway;
//
//    @Test
//    public void test() {
//
//        DelayDspOrder o1 = new DelayDspOrder();
//        o1.setOrderId("881830312535372108");
//        o1.setMainOrderId("31330882480");
//        DispatchResultDetail d1 = new DispatchResultDetail(o1);
//
//        TransportGroupInfo tg = new TransportGroupInfo();
//        tg.setTransportGroupId(895);
//        tg.setTransportGroupMode(1006);
//        Driver driver = new Driver();
//        driver.setDriverId("3452468");
//        driver.setTransportGroups(Lists.newArrayList(tg));
//        DriverAggregation da = new DriverAggregation();
//        da.setDriver(driver);
//        DelayDspOrder o2 = new DelayDspOrder();
//        o2.setOrderId("881830318171789834");
//        o2.setMainOrderId("31330882314");
//        String duid = new Duid(o2.getOrderId(), 8889).toString();
//        o2.setDuid(duid);
//        koranGateway.initDspContext(duid);
//        DispatchResultDetail d2 = new DispatchResultDetail(da, o2, 0, new Route("", 0.0, 0.0));
//
//        DispatchResult result = new DispatchResult();
//        result.setDetails(Lists.newArrayList(d1, d2));
//        supplyOrderService.taken(result);
//    }
//
//    @Test
//    public void testRedispatch() {
//
//        DelayDspOrder o1 = new DelayDspOrder();
//        o1.setOrderId("881830312535372108");
//        o1.setMainOrderId("31330882480");
//        supplyOrderService.redispatch(o1);
//    }
//}
