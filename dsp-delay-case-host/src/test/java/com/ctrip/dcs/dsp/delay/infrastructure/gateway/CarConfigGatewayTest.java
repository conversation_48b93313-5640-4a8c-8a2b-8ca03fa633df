//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
//import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
//import com.ctrip.dcs.dsp.delay.carconfig.Key;
//import com.ctrip.dcs.dsp.delay.carconfig.key.HeadTailLimitKey;
//import com.ctrip.dcs.dsp.delay.carconfig.key.OrderMileageKey;
//import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue;
//import com.ctrip.dcs.dsp.delay.carconfig.value.OrderMileageValue;
//import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
//import com.ctrip.dcs.dsp.delay.infrastructure.geteway.CarConfigGatewayImpl;
//import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
//import org.codehaus.jackson.type.TypeReference;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class CarConfigGatewayTest {
//
//    @Autowired
//    private CarConfigGatewayImpl carConfigGateway;
//
//    @Autowired
//    private TRocksProviderProxy tRocksProviderProxy;
//
//    @Test
//    public void testCarConfigQuery() {
//        String key = CommonConstant.RPC_CACHE_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + CarConfigCode.ORDER_MILEAGE_VALUE.getCode() + CommonConstant.APP_ID + CarConfigCode.ORDER_MILEAGE_VALUE.getVersion();
//        tRocksProviderProxy.del(key);
//        CarConfig carConfig1 = carConfigGateway.query(CarConfigCode.ORDER_MILEAGE_VALUE.getCode(), CarConfigCode.ORDER_MILEAGE_VALUE.getVersion());
//        CarConfig carConfig2 = carConfigGateway.query(CarConfigCode.ORDER_MILEAGE_VALUE.getCode(), CarConfigCode.ORDER_MILEAGE_VALUE.getVersion());
//        Assert.assertTrue(carConfig1.getUniqCode().equals(carConfig2.getUniqCode()));
//        List<OrderMileageValue> values = carConfig1.values(new TypeReference<List<OrderMileageValue>>(){}, new OrderMileageKey(Key.ALL_CITY, Key.ALL_CAR_TYPE));
//        Assert.assertNotNull(values);
//    }
//
//    @Test
//    public void testQueryHeadTailLimit() {
//        String key = CommonConstant.RPC_CACHE_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + CarConfigCode.HEAD_TAIL_LIMIT.getCode() + CommonConstant.APP_ID + CarConfigCode.HEAD_TAIL_LIMIT.getVersion();
//        tRocksProviderProxy.del(key);
//        CarConfig carConfig = carConfigGateway.query(CarConfigCode.HEAD_TAIL_LIMIT.getCode(), CarConfigCode.HEAD_TAIL_LIMIT.getVersion());
//        List<HeadTailLimitValue> values = carConfig.values(
//                new TypeReference<HeadTailLimitValue>() {
//                },
//                new HeadTailLimitKey("beijing_city", 117),
//                new HeadTailLimitKey(Key.ALL_CITY, Key.ALL_CAR_TYPE)
//        );
//        Assert.assertNotNull(values);
//    }
//}
