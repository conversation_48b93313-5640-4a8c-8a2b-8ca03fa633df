//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.gateway.KoranGateway;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class KoranGatewayTest {
//
//    @Autowired
//    private KoranGateway koranGateway;
//
//    @Test
//    public void testInitContext() {
//        koranGateway.initDspContext("881830177039152498-v2.0-0-8889-1:1:1-220510135440-2-5-0-0");
//    }
//}
