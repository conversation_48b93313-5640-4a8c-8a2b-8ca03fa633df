//package com.ctrip.dcs.dsp.delay.infrastructure.repository;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
//import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
//import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
//import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
//import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
//import com.ctrip.dcs.dsp.delay.util.JsonUtil;
//import com.google.common.collect.Lists;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class DelayDspTaskRepositoryTest {
//
//    @Autowired
//    private DelayDspTaskRepository repository;
//
//    @Autowired
//    private DelayTaskService service;
//
//    @Test
//    public void testDoubleInsert() {
//        DelayDspTask task = new DelayDspTask();
//        task.setCityId(1);
//        task.setCityCode("beijing_city");
//        task.setCarTypeId(117);
//        task.setTaskStatus(0);
//        task.setTaskSeq(0);
//        task.setBeginTime(new Date());
//        task.setEndTime(new Date());
//        task.setExecuteTime(new Date());
//        task.setExecuteTimeDeadline(new Date());
//        Long id1 = repository.save(task);
//        Long id2 = repository.save(task);
//        System.out.println(id1 + "," + id2);
//    }
//
//    @Test
//    public void test() {
//        SupplyOrder order = new SupplyOrder();
//        order.setOrderStatus(2);
//        order.setCityId(2);
//        order.setCityCode("shanghai_city");
//        order.setCarTypeId(117);
//        order.setSysExpectBookTime(new Date());
//        DelayDspTask task1 = service.queryOrCreate(order);
//        DelayDspTask task2 = service.queryOrCreate(order);
//        System.out.println(task1.getTaskId() + "," + task2.getTaskId());
//    }
//
//    @Test
//    public void testRecord() {
//        List<DelayDspTaskRecord> list = Lists.newArrayList(new DelayDspTaskRecord(1L, "1"), new DelayDspTaskRecord(1L, "2"));
//        repository.save(list);
//    }
//}
