//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
//import com.ctrip.dcs.dsp.delay.model.Driver;
//import com.ctrip.dcs.dsp.delay.model.DriverLeave;
//import com.ctrip.dcs.dsp.delay.qconfig.DelayDspChecksQConfig;
//import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO;
//import com.ctrip.dcs.dsp.delay.util.JsonUtil;
//import com.google.common.collect.Lists;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class DriverGatewayTest {
//
//    @Autowired
//    private DriverGateway driverGateway;
//
//    @Autowired
//    DelayDspChecksQConfig delayDspChecksQConfig;
//
//    @Test
//    public void test() {
//        Driver driver = driverGateway.query("1000033");
//        System.out.println(JsonUtil.toJson(driver));
//    }
//
//    @Test
//    public void testLeave() {
//        List<DriverLeave> list = driverGateway.queryLeave(Lists.newArrayList("1000033"));
//        Assert.assertNotNull(list);
//    }
//
//    @Test
//    public void test1() {
//        DelayDspCheckValueVO vo = delayDspChecksQConfig.getCheckValueVO(3);
//        List<String> list = vo.getOrderToOrderCheckList();
//        System.out.println(JsonUtil.toJson(list));
//
//    }
//
//
//}
