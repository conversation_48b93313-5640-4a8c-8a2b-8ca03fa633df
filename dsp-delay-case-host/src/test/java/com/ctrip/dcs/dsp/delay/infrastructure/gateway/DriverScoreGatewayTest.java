//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.infrastructure.geteway.DriverGatewayImpl;
//import com.ctrip.dcs.dsp.delay.model.DriverScore;
//import com.google.common.collect.Sets;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class DriverScoreGatewayTest {
//
//    @Autowired
//    private DriverGatewayImpl driverGateway;
//
//    @Test
//    public void testCarConfigQuery() {
//        List<DriverScore> driverScores = driverGateway.queryDriverScore(Sets.newHashSet("1000005"));
//        Assert.assertNotNull(driverScores);
//    }
//}
