//package com.ctrip.dcs.dsp.delay.application.service;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
//import com.ctrip.dcs.dsp.application.service.DispatchTaskApplicationService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class DispatchTaskApplicationServiceTest {
//
//    @Autowired
//    private DispatchTaskApplicationService dispatchTaskApplicationService;
//
//    @Test
//    public void test() {
//        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
//        request.setTaskId(569L);
//        dispatchTaskApplicationService.dispatch(request);
//    }
//}
