//package com.ctrip.dcs.dsp.delay.infrastructure.proxy;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.infrastructure.http.InventoryProxy;
//import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenDTO;
//import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenResultDTO;
//import com.google.common.collect.Lists;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class InventoryProxyTest {
//
//    @Autowired
//    private InventoryProxy proxy;
//
//    @Test
//    public void testBatchFrozen() {
//        List<FrozenDTO> list = Lists.newArrayList(new FrozenDTO("881833287438177423", "1000004"), new FrozenDTO("881833288611056640", "1000004"));
//        List<FrozenResultDTO> results = proxy.batchFrozen(list);
//        Assert.assertNotNull(results);
//    }
//
//    @Test
//    public void testUnFrozen() {
//        proxy.unfrozen("881833287438177423", "1000004");
//        Assert.assertTrue(true);
//    }
//}
