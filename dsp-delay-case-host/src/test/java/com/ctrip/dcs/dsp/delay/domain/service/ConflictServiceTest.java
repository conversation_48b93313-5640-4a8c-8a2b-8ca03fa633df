//package com.ctrip.dcs.dsp.delay.domain.service;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
//import com.ctrip.dcs.dsp.delay.model.ConflictInfo;
//import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
//import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
//import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
//import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
//import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
//import com.ctrip.dcs.dsp.delay.service.ConflictService;
//import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
//import com.google.common.collect.Lists;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class ConflictServiceTest {
//
//    @Autowired
//    private ConflictService conflictService;
//
//    @Autowired
//    private DispatchConfigService dispatchConfigService;
//
//    @Autowired
//    private DelayDspTaskRepository delayDspTaskRepository;
//
//    @Autowired
//    private DelayDspOrderRepository delayDspOrderRepository;
//
//    @Test
//    public void test() {
//        DelayDspOrder o1 = delayDspOrderRepository.queryByOrderId("881830519659057306");
//        DelayDspOrder o2 = delayDspOrderRepository.queryByOrderId("881830511227993453");
//        DelayDspTask task = delayDspTaskRepository.queryByTaskId(115L);
//        DispatcherConfig config = dispatchConfigService.buildDispatcherConfig(task);
//        DelayDspContext context = new DelayDspContext(task, Lists.newArrayList(), Lists.newArrayList(), config);
//        ConflictInfo conflict = conflictService.isConflict(context, o2, o1);
//        Assert.assertTrue(conflict.isConflict());
//    }
//}
