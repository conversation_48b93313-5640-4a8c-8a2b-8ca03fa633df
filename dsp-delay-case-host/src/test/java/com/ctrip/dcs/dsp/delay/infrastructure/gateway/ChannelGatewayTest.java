//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway;
//import com.ctrip.dcs.dsp.delay.model.ChannelNumber;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class ChannelGatewayTest {
//
//    @Autowired
//    private ChannelGateway gateway;
//
//    @Test
//    public void test() {
//        ChannelNumber channelNumber = gateway.queryChannelNumber(5L);
//        Assert.assertNotNull(channelNumber);
//    }
//}
