//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
//import com.ctrip.dcs.dsp.delay.util.JsonUtil;
//import com.google.common.collect.Lists;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class TRocksProviderProxyTest {
//
//    @Autowired
//    private TRocksProviderProxy tRocksProviderProxy;
//
//    @Test
//    public void test() {
//        List<String> values = tRocksProviderProxy.mget(Lists.newArrayList("a", "b"));
//        System.out.println(JsonUtil.toJson(values));
//    }
//
//    @Test
//    public void test1() {
//        String values = tRocksProviderProxy.get("a");
//        System.out.println(JsonUtil.toJson(values));
//    }
//
//    @Test
//    public void test2() throws InterruptedException {
//        Boolean bool = tRocksProviderProxy.setex("a", "b", 1);
//        String value1 = tRocksProviderProxy.get("a");
//        Thread.sleep(2000);
//        String value2 = tRocksProviderProxy.get("a");
//        System.out.println(JsonUtil.toJson(value2));
//    }
//
//    @Test
//    public void test3() {
//        boolean b1 = tRocksProviderProxy.tryAcquire("a", "b", 100);
//        boolean b2 = tRocksProviderProxy.tryAcquire("a", "b", 100);
//        System.out.println(JsonUtil.toJson(b1 + "," + b2));
//    }
//}
