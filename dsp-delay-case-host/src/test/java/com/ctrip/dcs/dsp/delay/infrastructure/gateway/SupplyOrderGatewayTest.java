//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
//import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
//import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
//import com.ctrip.dcs.dsp.delay.util.DateUtil;
//import com.google.common.collect.Lists;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//import java.util.Set;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class SupplyOrderGatewayTest {
//
//    @Autowired
//    private SupplyOrderGateway supplyOrderGateway;
//
//    @Test
//    public void testTaken() {
//        Integer taken = supplyOrderGateway.taken("881830177039152498", "1000033", "881830177039152498-v2.0-0-8889-1:1:1-************-2-5-0-0", 92);
//        Assert.assertEquals(taken.intValue(), OrderTakenCode.SUCCESS.getCode());
//    }
//
//    @Test
//    public void testQuery() {
//        SupplyOrder supplyOrder = supplyOrderGateway.query("881830177039152498");
//        Assert.assertEquals(supplyOrder.getUid(), "R616814755");
//    }
//
//    @Test
//    public void testRedispatch() {
//        supplyOrderGateway.redispatch("32101491521");
//    }
//
//    @Test
//    public void testXSku() {
//        Set<String> set = supplyOrderGateway.queryOrderXSkuCode("32103596744");
//        Assert.assertNotNull(set);
//    }
//
//    @Test
//    public void testQueryByDriver() {
//        List<SupplyOrder> list = supplyOrderGateway.queryByDriver(
//                Lists.newArrayList("1000033", "94"),
//                DateUtil.parseDate("2022-09-05 10:00:00"),
//                DateUtil.parseDate("2022-09-05 14:00:00"),
//                DateUtil.parseDate("2022-09-05 18:00:00"),
//                DateUtil.parseDate("2022-09-05 22:00:00")
//        );
//        Assert.assertNotNull(list);
//    }
//}
