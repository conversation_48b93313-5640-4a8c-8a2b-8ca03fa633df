//package com.ctrip.dcs.dsp.delay.infrastructure.gateway;
//
//import com.ctrip.SOASpringBootServletInitializer;
//import com.ctrip.dcs.dsp.delay.gateway.TrafficControlGateway;
//import com.ctrip.dcs.dsp.delay.util.DateUtil;
//import com.google.common.collect.Sets;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.Set;
//
///**
// * <AUTHOR>
// */
//@WebAppConfiguration
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = SOASpringBootServletInitializer.class)
//public class TrafficControlGatewayTest {
//
//    @Autowired
//    private TrafficControlGateway trafficControlGateway;
//
//    @Test
//    public void test() {
//        Set<String> set = trafficControlGateway.queryLimitLicense(
//                1,
//                DateUtil.parseDate("2022-05-20 08:00:00"),
//                DateUtil.parseDate("2022-05-20 19:59:00"),
//                Sets.newHashSet(
//                        "京A11111",
//                        "京A22222",
//                        "京A33333",
//                        "京A44444",
//                        "京A55555"
//                        )
//        );
//        Assert.assertEquals(set.size(), 1);
//    }
//}
