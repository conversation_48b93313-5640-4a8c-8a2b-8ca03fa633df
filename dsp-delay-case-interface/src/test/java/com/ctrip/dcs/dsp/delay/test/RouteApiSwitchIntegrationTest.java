package com.ctrip.dcs.dsp.delay.test;

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.GeoGatewayImpl;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.Top20CityConfig;
import com.ctrip.dcs.dsp.delay.qconfig.PeakHourConfig;
import com.ctrip.dcs.dsp.delay.qconfig.RouteApiSwitchConfig;
import com.ctrip.dcs.dsp.delay.service.RouteComparisonService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 路径接口切换集成测试
 * 测试整个流程：配置验证、接口切换逻辑、异步监控、延迟消息处理和监控埋点
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RouteApiSwitchIntegrationTest {

    @Autowired
    private GeoGatewayImpl geoGateway;

    @Autowired
    private Top20CityConfig top20CityConfig;

    @Autowired
    private PeakHourConfig peakHourConfig;

    @Autowired
    private RouteApiSwitchConfig routeApiSwitchConfig;

    @Autowired
    private RouteComparisonService routeComparisonService;

    /**
     * 测试配置验证
     */
    @Test
    public void testConfigValidation() {
        System.out.println("=== 测试配置验证 ===");
        
        // 测试Top20城市配置
        System.out.println("Top20城市配置测试：");
        System.out.println("北京(1)是否为Top20城市: " + top20CityConfig.isTop20City(1));
        System.out.println("上海(2)是否为Top20城市: " + top20CityConfig.isTop20City(2));
        System.out.println("非Top20城市(100)测试: " + top20CityConfig.isTop20City(100));
        
        // 测试高峰期配置
        System.out.println("\n高峰期配置测试：");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date peakTime = sdf.parse("2024-01-01 08:00:00"); // 早高峰
            Date nonPeakTime = sdf.parse("2024-01-01 14:00:00"); // 非高峰
            
            System.out.println("北京早高峰(08:00)测试: " + peakHourConfig.isPeakHour(1, peakTime));
            System.out.println("北京非高峰(14:00)测试: " + peakHourConfig.isPeakHour(1, nonPeakTime));
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // 测试开关配置
        System.out.println("\n开关配置测试：");
        System.out.println("是否启用新接口: " + routeApiSwitchConfig.shouldUseNewApi());
        System.out.println("是否直接切换: " + routeApiSwitchConfig.shouldDirectSwitch());
        System.out.println("是否启用异步监控: " + routeApiSwitchConfig.shouldEnableAsyncMonitor());
        System.out.println("是否启用延迟消息: " + routeApiSwitchConfig.shouldEnableDelayMessage());
        System.out.println("是否启用监控埋点: " + routeApiSwitchConfig.shouldEnableMonitor());
    }

    /**
     * 测试接口切换逻辑
     */
    @Test
    public void testApiSwitchLogic() {
        System.out.println("=== 测试接口切换逻辑 ===");
        
        // 创建测试数据
        List<Position> positions = createTestPositions();
        Integer cityId = 1; // 北京，Top20城市
        String userOrderId = "test_order_001";
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date peakTime = sdf.parse("2024-01-01 08:00:00"); // 早高峰
            Date nonPeakTime = sdf.parse("2024-01-01 14:00:00"); // 非高峰
            
            // 测试高峰期调用
            System.out.println("高峰期调用测试：");
            List<Route> peakRoutes = geoGateway.queryRoutes(cityId, positions, peakTime, userOrderId);
            System.out.println("高峰期路径数量: " + peakRoutes.size());
            
            // 测试非高峰期调用
            System.out.println("非高峰期调用测试：");
            List<Route> nonPeakRoutes = geoGateway.queryRoutes(cityId, positions, nonPeakTime, userOrderId);
            System.out.println("非高峰期路径数量: " + nonPeakRoutes.size());
            
            // 测试非Top20城市
            System.out.println("非Top20城市调用测试：");
            List<Route> nonTop20Routes = geoGateway.queryRoutes(100, positions, peakTime, userOrderId);
            System.out.println("非Top20城市路径数量: " + nonTop20Routes.size());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试路径比较服务
     */
    @Test
    public void testRouteComparison() {
        System.out.println("=== 测试路径比较服务 ===");
        
        // 创建测试数据
        List<Position> positions = createTestPositions();
        List<Route> realTimeRoutes = createTestRoutes("realtime");
        List<Route> futureRoutes = createTestRoutes("future");
        List<Route> actualRoutes = createTestRoutes("actual");
        
        Integer cityId = 1;
        Date useTime = new Date();
        
        // 测试实时与未来预估比较
        System.out.println("实时与未来预估比较测试：");
        routeComparisonService.compareRealTimeAndFuture(cityId, useTime, positions, realTimeRoutes, futureRoutes);
        
        // 测试实际与之前预估比较
        System.out.println("实际与之前预估比较测试：");
        routeComparisonService.compareActualAndPrevious(cityId, useTime, positions, actualRoutes, futureRoutes);
        
        // 测试三种结果综合比较
        System.out.println("三种结果综合比较测试：");
        routeComparisonService.compareAllThreeResults(cityId, useTime, positions, realTimeRoutes, futureRoutes, actualRoutes);
        
        // 测试统计信息计算
        System.out.println("统计信息计算测试：");
        RouteComparisonService.RouteStatistics statistics = routeComparisonService.calculateStatistics(realTimeRoutes);
        System.out.println("路径数量: " + statistics.getCount());
        System.out.println("平均距离: " + statistics.getAvgDistance());
        System.out.println("平均时长: " + statistics.getAvgDuration());
    }

    /**
     * 测试缓存key生成
     */
    @Test
    public void testCacheKeyGeneration() {
        System.out.println("=== 测试缓存key生成 ===");
        
        Long taskId = 12345L;
        String hash = "test_hash_001";
        Date useTime = new Date();
        
        // 测试原有key生成
        String oldKey = Route.toKey(taskId, hash);
        System.out.println("原有缓存key: " + oldKey);
        
        // 测试带时间维度的key生成
        String newKey = Route.toKey(taskId, hash, useTime);
        System.out.println("带时间维度缓存key: " + newKey);
        
        // 验证key的唯一性
        Date anotherTime = new Date(useTime.getTime() + 3600000); // 1小时后
        String anotherKey = Route.toKey(taskId, hash, anotherTime);
        System.out.println("不同时间缓存key: " + anotherKey);
        System.out.println("key是否不同: " + !newKey.equals(anotherKey));
    }

    /**
     * 创建测试位置数据
     */
    private List<Position> createTestPositions() {
        List<Position> positions = Lists.newArrayList();
        
        // 创建几个测试位置
        Position position1 = new Position();
        position1.setFromLatitude(39.9042);
        position1.setFromLongitude(116.4074);
        position1.setToLatitude(39.9142);
        position1.setToLongitude(116.4174);
        positions.add(position1);
        
        Position position2 = new Position();
        position2.setFromLatitude(39.9242);
        position2.setFromLongitude(116.4274);
        position2.setToLatitude(39.9342);
        position2.setToLongitude(116.4374);
        positions.add(position2);
        
        return positions;
    }

    /**
     * 创建测试路径数据
     */
    private List<Route> createTestRoutes(String type) {
        List<Route> routes = Lists.newArrayList();
        
        // 根据类型创建不同的测试数据
        double baseDuration = 30.0;
        double baseDistance = 10.0;
        
        if ("future".equals(type)) {
            baseDuration += 5.0; // 未来预估稍长
        } else if ("actual".equals(type)) {
            baseDuration += 2.0; // 实际结果介于中间
        }
        
        routes.add(new Route("hash1", baseDistance, baseDuration));
        routes.add(new Route("hash2", baseDistance + 2.0, baseDuration + 3.0));
        
        return routes;
    }
}
