package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayUnfrozenDriverInventoryConsumerTest extends Specification {

    @Mock
    Logger logger
    @Mock
    InventoryGateway inventoryGateway
    @InjectMocks
    DelayUnfrozenDriverInventoryConsumer delayUnfrozenDriverInventoryConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        when:
        BaseMessage message1 = new BaseMessage();
        BaseMessage message2 = new BaseMessage();
        message2.setProperty("orderId", "123")
        BaseMessage message3 = new BaseMessage();
        message3.setProperty("orderId", "123")
        message3.setProperty("driverId", "456")
        delayUnfrozenDriverInventoryConsumer.onMessage(message1)
        delayUnfrozenDriverInventoryConsumer.onMessage(message2)
        delayUnfrozenDriverInventoryConsumer.onMessage(message3)

        then:
        verify(inventoryGateway, times(1)).unfrozen(anyString(), anyString())
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme