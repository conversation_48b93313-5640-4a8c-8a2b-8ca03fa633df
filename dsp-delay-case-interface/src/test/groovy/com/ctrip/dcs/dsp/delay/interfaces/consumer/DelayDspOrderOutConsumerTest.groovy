package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.enums.OrderSource
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper

import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService
import com.ctrip.dcs.dsp.delay.service.SupplyOrderService
import com.ctrip.dcs.dsp.delay.service.TakenOrderService
import com.ctrip.igt.framework.common.clogging.Logger
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.Message
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayDspOrderOutConsumerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DelayDspTaskRecordMapper delayDspTaskRecordMapper
    @Mock
    SupplyOrderService supplyOrderService
    @Mock
    DelayDspTaskRecord delayDspTaskRecord
    @Mock
    private TakenOrderService takenOrderService;
    @Mock
    private RedispatchOrderService redispatchOrderService;
    @Mock
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;
    @Mock
    private SupplyOrder supplyOrder;
    @Mock
    Message message;
    @Mock
    private SelfDriverOrderGateway driverOrderGateway;
    @InjectMocks
    DelayDspOrderOutConsumer delayDspOrderOutConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(delayDspTaskRecord.getOrderSource()).thenReturn(OrderSource.QUNAR.name()).thenReturn(OrderSource.QUNAR.name()).thenReturn(OrderSource.CTRIP.name()).thenReturn(OrderSource.CTRIP.name())
        when(delayDspTaskRecordMapper.query(anyLong())).thenReturn(delayDspTaskRecord);
        when(message.getLongProperty(anyString())).thenReturn(0L);
        when(message.getIntProperty(anyString())).thenReturn(0).thenReturn(1).thenReturn(0).thenReturn(1);

        when:
        def result =delayDspOrderOutConsumer.onMessage(message);
        delayDspOrderOutConsumer.onMessage(message);
        delayDspOrderOutConsumer.onMessage(message);
        delayDspOrderOutConsumer.onMessage(message);

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme