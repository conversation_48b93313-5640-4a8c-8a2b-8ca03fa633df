package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspOrderMapper
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayDspOrderOutCheckConsumerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DelayDspTaskRecordMapper delayDspTaskRecordMapper
    @Mock
    DelayDspOrderMapper delayDspOrderMapper
    @Mock
    SelfDispatcherOrderGateway selfDispatcherOrderGateway
    @Mock
    SupplyOrderGateway supplyOrderGateway
    @Mock
    BaseMessage message
    @Mock
    DelayDspTask task
    @Mock
    SupplyOrder supplyOrder
    @Mock
    DelayDspTaskRecord taskRecord
    @Mock
    DelayDspOrder delayDspOrder
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig
    @Mock
    private MessageProducer messageProducer
    @InjectMocks
    DelayDspOrderOutCheckConsumer delayDspOrderOutCheckConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(taskRecord.getOrderId()).thenReturn("1")
        when(delayDspOrder.getMainOrderId()).thenReturn("1")
        when(supplyOrder.getOrderStatus()).thenReturn(2)
        when(supplyOrder.getOrderStatusDetail()).thenReturn(201)
        when(delayDspTaskRecordMapper.query(anyLong())).thenReturn(new DelayDspTaskRecord())
        when(delayDspOrderMapper.queryByOrderId(anyString())).thenReturn(delayDspOrder)
        when(selfDispatcherOrderGateway.queryByUserOrderId(anyString())).thenReturn(supplyOrder)
        when(supplyOrderGateway.query(anyString())).thenReturn(supplyOrder)

        when:
        delayDspOrderOutCheckConsumer.onMessage(message)

        then:
        delayDspOrderOutCheckConsumer.isServiceConfirm(supplyOrder)
    }

    def "test query Supply Order"() {
        given:
        when(taskRecord.getOrderId()).thenReturn(orderId)
        when(delayDspOrder.getMainOrderId()).thenReturn(mainOrderId)
        when(delayDspOrder.getOrderSource()).thenReturn(source)
        when(delayDspTaskRecordMapper.query(anyLong())).thenReturn(new DelayDspTaskRecord())
        when(delayDspOrderMapper.queryByOrderId(anyString())).thenReturn(delayDspOrder)
        when(selfDispatcherOrderGateway.queryByUserOrderId(anyString())).thenReturn(supply)
        when(supplyOrderGateway.query(anyString())).thenReturn(supply)

        when:
        SupplyOrder o = delayDspOrderOutCheckConsumer.querySupplyOrder(new DelayDspTaskRecord())

        then:
        if (o == null) {
            result == null
        } else {
            o.orderId == result.orderId
        }

        where:
        orderId | mainOrderId | source  | supply      || result
        null    | null        | null    | null        || null
        "1"     | null        | null    | null        || null
        "1"     | "1"         | "CTRIP" | new SupplyOrder(orderId: "1") || new SupplyOrder(orderId: "1")
        "1"     | "1"         | "QUNAR" | new SupplyOrder(orderId: "1") || new SupplyOrder(orderId: "1")
    }

    def "test max count"() {
        given:
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(message.getIntProperty(anyString())).thenReturn(5)
        when(taskRecord.getOrderId()).thenReturn("1")
        when(delayDspOrder.getMainOrderId()).thenReturn("1")
        when(supplyOrder.getOrderStatus()).thenReturn(2)
        when(supplyOrder.getOrderStatusDetail()).thenReturn(201)
        when(delayDspTaskRecordMapper.query(anyLong())).thenReturn(new DelayDspTaskRecord())
        when(delayDspOrderMapper.queryByOrderId(anyString())).thenReturn(delayDspOrder)
        when(selfDispatcherOrderGateway.queryByUserOrderId(anyString())).thenReturn(supplyOrder)
        when(supplyOrderGateway.query(anyString())).thenReturn(supplyOrder)

        when:
        delayDspOrderOutCheckConsumer.onMessage(message)

        then:
        delayDspOrderOutCheckConsumer.isServiceConfirm(supplyOrder)
    }

    def "test exception"() {
        given:
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(message.getIntProperty(anyString())).thenReturn(5)
        when(taskRecord.getOrderId()).thenReturn("1")
        when(delayDspOrder.getMainOrderId()).thenReturn("1")
        when(supplyOrder.getOrderStatus()).thenReturn(2)
        when(supplyOrder.getOrderStatusDetail()).thenReturn(201)
        when(delayDspTaskRecordMapper.query(anyLong())).thenReturn(new DelayDspTaskRecord())
        when(delayDspOrderMapper.queryByOrderId(anyString())).thenReturn(delayDspOrder)
        when(selfDispatcherOrderGateway.queryByUserOrderId(anyString())).thenReturn(supplyOrder)
        when(supplyOrderGateway.query(anyString())).thenThrow(new RuntimeException())

        when:
        def resOut = delayDspOrderOutCheckConsumer.onMessage(message)

        then:
        resOut == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme