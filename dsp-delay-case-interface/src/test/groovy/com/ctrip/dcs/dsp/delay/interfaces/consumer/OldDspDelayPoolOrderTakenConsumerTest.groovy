package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Assert
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 09:36
 */
class OldDspDelayPoolOrderTakenConsumerTest extends Specification {
    def testObj = new OldDspDelayPoolOrderTakenConsumer()
    def logger = Mock(Logger)
    def delayDspApplicationService = Mock(DelayDspOrderApplicationService)

    def setup() {
        testObj.delayDspApplicationService = delayDspApplicationService
        testObj.logger = logger
    }

    @Unroll
    def "test onMessage"() {
        given:
        when:
        testObj.onMessage(message)

        then:
        Assert.assertTrue(Objects.nonNull(message))

        where:
        message           || expectedResult
        new BaseMessage() || null
        buildMessage1()   || null
        buildMessage2()   || null
    }

    def "test onMessage failed"() {
        given:
        delayDspApplicationService.syncDelayDspOrder(_, _) >> { throw new RuntimeException("") }

        when:
        testObj.onMessage(buildMessage2());

        then:
        def ex = thrown(RuntimeException);
        Assert.assertTrue(ex != null)
    }

    def BaseMessage buildMessage1() {
        BaseMessage message = new BaseMessage();
        message.setProperty("orderId","1111");
        return message;
    }

    def BaseMessage buildMessage2() {
        BaseMessage message = new BaseMessage();
        message.setProperty("orderId","1111");
        message.setProperty("driverId","2222");
        return message;
    }
}
