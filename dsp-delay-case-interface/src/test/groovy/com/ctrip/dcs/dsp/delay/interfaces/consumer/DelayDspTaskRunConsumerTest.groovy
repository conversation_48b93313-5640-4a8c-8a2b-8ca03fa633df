package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.redis.RedisDistributedLockService
import com.ctrip.dcs.dsp.application.service.DelayDspTaskApplicationService
import com.ctrip.dcs.dsp.application.service.DispatchTaskApplicationService
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.qmq.Message
import qunar.tc.qmq.NeedRetryException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayDspTaskRunConsumerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DispatchTaskApplicationService dispatchTaskApplicationService
    @Mock
    DelayDspTaskApplicationService delayDispatchApplicationService
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig
    @Mock
    GrayscaleQConfig grayscaleQConfig
    @Mock
    RedisDistributedLockService distributedLockService;
    @Mock
    DLock lock
    @Mock
    Message message
    @InjectMocks
    DelayDspTaskRunConsumer delayDspTaskRunConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE)
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(Boolean.TRUE)

        when:
        delayDspTaskRunConsumer.onMessage(message)

        then:
        verify(dispatchTaskApplicationService).dispatch(any())
    }

    def "test on Message 1"() {
        given:
        when(grayscaleQConfig.isGrayscaleCity(any())).thenReturn(Boolean.FALSE)
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.FALSE)
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(Boolean.TRUE)

        when:
        delayDspTaskRunConsumer.onMessage(message)

        then:
        verify(delayDispatchApplicationService).dispatch(any())
    }

    def "test on Message 2"() {
        given:
        when(grayscaleQConfig.isGrayscaleCity(any())).thenReturn(Boolean.FALSE)
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.FALSE)
        when(delayDispatchApplicationService.dispatch(any())).thenThrow(new RuntimeException("test"))
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(Boolean.TRUE)

        when:
        delayDspTaskRunConsumer.onMessage(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "DelayDspTaskRunConsumerRetry"
    }

    def "test on Message 3"() {
        given:
        when(grayscaleQConfig.isGrayscaleCity(any())).thenReturn(Boolean.FALSE)
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.FALSE)
        when(delayDispatchApplicationService.dispatch(any())).thenThrow(new RuntimeException("test"))
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(lock.tryLock()).thenReturn(Boolean.FALSE)

        when:
        delayDspTaskRunConsumer.onMessage(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getMessage() == "DelayDspTaskRunConsumerRetry"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme