package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"orderId"})
public class InsertDelayDspOrderExecutor extends AbstractRpcExecutor<InsertDelayDspOrderRequestType, InsertDelayDspOrderResponseType> implements Validator<InsertDelayDspOrderRequestType> {

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Override
    public InsertDelayDspOrderResponseType execute(InsertDelayDspOrderRequestType request) {
        InsertDelayDspOrderResponseType response = delayDspApplicationService.insertDelayDspOrder(request);
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<InsertDelayDspOrderRequestType> validator) {
        validator.ruleFor("orderId").notNull().notEmpty();
    }
}
