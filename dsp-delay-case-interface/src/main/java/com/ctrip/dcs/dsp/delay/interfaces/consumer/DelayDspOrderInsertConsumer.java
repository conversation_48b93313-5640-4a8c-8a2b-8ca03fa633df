package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.request.SaveAdjacencyRouteRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspTaskApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.gateway.KoranGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspOrderInsertConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderInsertConsumer.class);

    @Autowired
    private DelayDspTaskApplicationService delayDspTaskApplicationService;

    @Autowired
    private KoranGateway koranGateway;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            Long taskId = message.getLongProperty("taskId");
            String orderId = message.getStringProperty("orderId");
            String duid = message.getStringProperty("duid");
            String taskType = message.getStringProperty("taskType");
            String orderSource = message.getStringProperty("orderSource");
            if (StringUtils.isNotBlank(duid) && StringUtils.equalsIgnoreCase(orderSource, OrderSource.QUNAR.name())) {
                koranGateway.initDspContext(duid);
            }
            if (StringUtils.isBlank(taskType) || DelayDspTaskType.DP.name().equalsIgnoreCase(taskType)) {
                delayDspTaskApplicationService.saveAdjacencyEdge(new SaveAdjacencyRouteRequest(taskId, orderId));
            }
        } catch (Exception e) {
            logger.error("consume qmq dcs.dsp.delay.order.change! error!", e);
        }
    }
}
