package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.OutType;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService;
import com.ctrip.dcs.dsp.delay.service.SupplyOrderService;
import com.ctrip.dcs.dsp.delay.service.TakenOrderService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspOrderOutConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderOutConsumer.class);

    @Autowired
    private DelayDspTaskRecordMapper delayDspTaskRecordMapper;

    @Autowired
    private SupplyOrderService supplyOrderService;

    @Autowired
    private TakenOrderService takenOrderService;

    @Autowired
    private RedispatchOrderService redispatchOrderService;

    /**
     * 出池应单 or 改派
     * @param message
     */
    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_ORDER_OUT_SUBJECT, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            Long recordId = message.getLongProperty("recorde_id");
            Integer outType = message.getIntProperty("out_type");
            DelayDspTaskRecord record = delayDspTaskRecordMapper.query(recordId);
            if (OutType.isTaken(outType)) {
                taken(record);
                return;
            }
            redispatch(record);
        } catch (Exception e) {
            logger.error("consume qmq dcs.dsp.delay.order.change! error!", e);
        }
    }

    private void redispatch(DelayDspTaskRecord record) {
        logger.info("DelayDspOrderOutConsumer_redispatch", JsonUtil.toJson(record));
        if (Objects.isNull(record)) {
            return;
        }
        if (OrderSource.isCtrip(record.getOrderSource())) {
            redispatchOrderService.redispatch(record);
            return;
        }
        supplyOrderService.redispatch(record);
    }

    private void taken(DelayDspTaskRecord record) {
        logger.info("DelayDspOrderOutConsumer_taken", JsonUtil.toJson(record));
        if (Objects.isNull(record)) {
            return;
        }
        if (OrderSource.isCtrip(record.getOrderSource())) {
            takenOrderService.taken(record);
            return;
        }
        supplyOrderService.taken(record);
    }
}
