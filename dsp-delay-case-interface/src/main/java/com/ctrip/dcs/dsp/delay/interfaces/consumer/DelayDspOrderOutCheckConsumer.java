package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatusDetail;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspOrderMapper;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;
import java.util.Objects;

/**
 * 检查延后派订单是否出池
 * <AUTHOR>
 */
@Component
public class DelayDspOrderOutCheckConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderOutCheckConsumer.class);

    private static final int MAX_CHECK_TIMES = 3;

    private static final int MAX_QMQ_RETRY_TIMES = 5;

    @Autowired
    private DelayDspTaskRecordMapper delayDspTaskRecordMapper;

    @Autowired
    private DelayDspOrderMapper delayDspOrderMapper;

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Autowired
    private SupplyOrderGateway supplyOrderGateway;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_ORDER_OUT_CHECK_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            Long recordId = message.getLongProperty("recorde_id");
            Integer outType = message.getIntProperty("out_type");
            Integer checkTimes = message.getIntProperty("check_times");
            if (recordId == 0L) {
                return;
            }
            logger.info("DelayDspOrderOutCheckConsumer_onMessage", "recordId:{}", recordId);
            logger.info("DelayDspOrderOutCheckConsumer_onMessage", "outType:{}", outType);
            logger.info("DelayDspOrderOutCheckConsumer_onMessage", "checkTimes:{}", checkTimes);
            DelayDspTaskRecord record = delayDspTaskRecordMapper.query(recordId);
            logger.info("DelayDspOrderOutCheckConsumer_query", JsonUtil.toJson(record));
            if (record == null || StringUtils.isBlank(record.getOrderId())) {
                return;
            }
            if(Objects.nonNull(record.getMatchRecordId())){
                return;
            }
            SupplyOrder supplyOrder = querySupplyOrder(record);
            if (supplyOrder == null) {
                throw new BizException("supply order is null");
            }

            if (checkTimes > MAX_CHECK_TIMES || message.times() > MAX_QMQ_RETRY_TIMES) {
                logger.error("DelayDspOrderOutCheckConsumerError", "orderId:{} is not out", supplyOrder.getOrderId());
                MetricsUtil.recordValue("dispatch.delay.order.out.timeout", 1);
                return;
            }
            if (isServiceConfirm(supplyOrder)) {
                Map<String, Object> data = ImmutableMap.<String, Object>builder()
                        .put("recorde_id", recordId)
                        .put("out_type", outType)
                        .put("check_times", ++checkTimes)
                        .build();
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_ORDER_OUT_SUBJECT, data);
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_OUT_CHECK_SUBJECT, data, delayDspCommonQConfig.getCheckOrderOutMillisecond());
            }
        } catch (Exception e) {
            logger.warn("DelayDspOrderOutCheckConsumerError", e);
            //60秒后再来重试
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "DelayDspOrderOutCheck");
        }
    }

    public SupplyOrder querySupplyOrder(DelayDspTaskRecord record) {
        DelayDspOrder delayDspOrder = delayDspOrderMapper.queryByOrderId(record.getOrderId());
        if (delayDspOrder == null || StringUtils.isBlank(delayDspOrder.getMainOrderId())) {
            return null;
        }
        if (OrderSource.isCtrip(delayDspOrder.getOrderSource())) {
            return selfDispatcherOrderGateway.queryByUserOrderId(delayDspOrder.getMainOrderId());
        }
        return supplyOrderGateway.query(delayDspOrder.getMainOrderId());
    }

    public boolean isServiceConfirm(SupplyOrder order) {
        if (order == null ) {
            return false;
        }
        return Objects.equals(order.getOrderStatus(), SupplyOrderStatus.SEND_ORDER.getCode()) && Objects.equals(order.getOrderStatusDetail(), SupplyOrderStatusDetail.SERVICE_CONFIRM.getCode());
    }
}
