package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import javax.annotation.Resource;

/**
 * 老流程同步KM虚拟派
 * <AUTHOR>
 * @since 2024/8/15 17:22
 */
@Component
public class OldDspDelayPoolOrderTakenConsumer {
    
    private static Logger logger = LoggerFactory.getLogger(OldDspDelayPoolOrderTakenConsumer.class);
    
    @Resource
    private DelayDspOrderApplicationService delayDspApplicationService;
    
    
    @QmqLogTag(tagKeys = {"orderId","driverId"})
    @QmqConsumer(prefix = CommonConstant.CAR_QB_DSP_DELAY_POOL_ORDER_TAKEN, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            String orderId = message.getStringProperty("orderId");
            String driverId = message.getStringProperty("driverId");
            if (StringUtils.isBlank(orderId) || StringUtils.isBlank(driverId)) {
                logger.error("car_qb_dsp_delay_pool_order_taken_paramValid", "orderId:" + orderId + " ,driverId:" + driverId);
                return;
            }
            delayDspApplicationService.syncDelayDspOrder(orderId, driverId);
        } catch(Exception ex) {
            MetricsUtil.recordValue("sync.delay.dsp.order.failed", 1);
            logger.error("car_qb_dsp_delay_pool_order_taken_error", "sync order is failed.", ex, Maps.newHashMap());
            throw ex;
        }
    }
    
}
