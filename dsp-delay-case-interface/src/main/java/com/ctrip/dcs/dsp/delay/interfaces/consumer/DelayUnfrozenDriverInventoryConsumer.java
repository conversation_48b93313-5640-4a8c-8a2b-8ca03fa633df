package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;


/**
 * <AUTHOR>
 */
@Component
public class DelayUnfrozenDriverInventoryConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayUnfrozenDriverInventoryConsumer.class);

    @Autowired
    private InventoryGateway inventoryGateway;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DELAY_ORDER_DRIVER_INVENTORY_UNFROZEN, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            String orderId = message.getStringProperty("orderId");
            if (StringUtils.isBlank(orderId)) {
                return;
            }
            String driverId = message.getStringProperty("driverId");
            if (StringUtils.isBlank(orderId)) {
                return;
            }
            inventoryGateway.unfrozen(orderId, driverId);
        } catch (Exception e) {
            logger.error("consume dcs.dsp.delay.order.driver.inventory.unfrozen error", e);
        }
    }
}
