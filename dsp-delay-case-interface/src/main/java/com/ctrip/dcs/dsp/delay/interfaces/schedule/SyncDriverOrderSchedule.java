package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.enums.CarType;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.filter.impl.CoopModeFilter;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 开城使用
 * <AUTHOR>
 */
@Component
public class SyncDriverOrderSchedule {

    private static final Logger logger = LoggerFactory.getLogger(SyncDriverOrderSchedule.class);

    private static final Set<Integer> CAR_TYPE = Sets.newHashSet(
            CarType.ECONOMIC_CAR_TYPE.getCode(),
            CarType.COMFORTABLE_CAR_TYPE.getCode(),
            CarType.BUSINESS_CAR_TYPE.getCode()
    );

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private SupplyOrderGateway supplyOrderGateway;

    @Autowired
    private CoopModeFilter coopModeFilter;

    @QSchedule("dcs.dsp.delay.sync.driver.order.task")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        logger.info("SyncDriverOrderSchedule", "The task begins to execute!");
        Integer cityId = parameter.getProperty("cityId", Integer.class);
        if (cityId == null) {
            logger.info("SyncDriverOrderSchedule", "there is no cityId!");
            return;
        }
        for (Integer carTypeId : CAR_TYPE) {
            List<Driver> drivers = driverGateway.query(cityId, carTypeId,null);
            if (CollectionUtils.isEmpty(drivers)) {
                logger.info("SyncDriverOrderSchedule", "there is no driver,city id {}, car type id {}", cityId, carTypeId);
                continue;
            }

            List<String> driverIds = drivers.stream()
                    .filter(d -> coopModeFilter.filter(d, null))
                    .map(Driver::getDriverId)
                    .collect(Collectors.toList());
            List<SupplyOrder> orders = supplyOrderGateway.queryByDriver(driverIds);
            for (SupplyOrder order : orders) {
                try {
                    logger.info("SyncDriverOrderSchedule", "insert driver taken order. orderId {}", order.getOrderId());
                    delayDspApplicationService.insertTakenDelayOrder(order.getOrderId(), OrderSource.QUNAR.name());
                } catch (Exception e) {
                    logger.error(e);
                }
            }
        }
        logger.info("SyncDriverOrderSchedule", "The task is completed!");
    }
}
