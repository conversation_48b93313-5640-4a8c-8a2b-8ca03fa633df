package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class OrderTakenConsumer {

    private static final Logger logger = LoggerFactory.getLogger(OrderTakenConsumer.class);

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.ORDER_TAKEN_SUBJECT, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            String supplyOrderIds = message.getStringProperty("supplyOrderIds");
            if (StringUtils.isBlank(supplyOrderIds)) {
                return;
            }
            int ota = message.getIntProperty("ota");
            if (ota == YesOrNo.YES.getCode()) {
                return;
            }
            List<String> orderIds = Splitter.on(",").splitToList(supplyOrderIds);
            for (String orderId : orderIds) {
                delayDspApplicationService.insertTakenDelayOrder(orderId, OrderSource.QUNAR.name());
            }

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_TAKEN! error!", e);
        }
    }

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = CommonConstant.APP_ID)
    public void onMessage1(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            if (StringUtils.isBlank(dspOrderId)) {
                return;
            }
            delayDspApplicationService.insertTakenDelayOrder(dspOrderId, OrderSource.CTRIP.name());

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_TAKEN! error!", e);
        }
    }
}
