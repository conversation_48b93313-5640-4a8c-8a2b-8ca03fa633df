package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.GeoGatewayImpl;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.RouteApiSwitchConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.RouteMonitorUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Date;
import java.util.List;

/**
 * 延迟路径预估消息消费者
 * 处理延迟消息，在真实用车时间调用路径预估接口获取实际结果
 * 
 * <AUTHOR>
 */
@Component
public class DelayedRouteEstimateConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayedRouteEstimateConsumer.class);

    @Autowired
    private GeoGatewayImpl geoGateway;

    @Autowired
    private RouteApiSwitchConfig routeApiSwitchConfig;

    /**
     * 处理延迟路径预估消息
     * 
     * @param message 消息
     */
    @QmqConsumer(prefix = "delayed_route_estimate", consumerGroup = "dsp-delay-case")
    public void handleDelayedRouteEstimate(Message message) {
        try {
            logger.info("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate", "received message: {}", message.getStringProperty("data"));

            // 解析消息数据
            Integer cityId = message.getIntProperty("cityId");
            Date useTime = (Date) message.getProperty("useTime");
            String userOrderId = message.getStringProperty("userOrderId");
            Date originalCallTime = (Date) message.getProperty("callTime");
            
            String positionsJson = message.getStringProperty("positions");
            List<Position> positions = JsonUtil.fromJson(positionsJson, new TypeReference<List<Position>>() {});

            if (cityId == null || useTime == null || CollectionUtils.isEmpty(positions)) {
                logger.warn("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate", "invalid message data");
                return;
            }

            // 在真实用车时间调用路径预估接口
            processDelayedRouteEstimate(cityId, useTime, userOrderId, positions, originalCallTime);

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate error", e);
        }
    }

    /**
     * 处理延迟路径预估
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @param originalCallTime 原始调用时间
     */
    private void processDelayedRouteEstimate(Integer cityId, Date useTime, String userOrderId, 
                                           List<Position> positions, Date originalCallTime) {
        try {
            Date actualCallTime = new Date();
            
            // 调用新接口获取实际结果
            List<Route> actualRoutes = geoGateway.queryRoutesNew(cityId, useTime, userOrderId, positions);
            
            if (CollectionUtils.isEmpty(actualRoutes)) {
                logger.warn("DelayedRouteEstimateConsumer.processDelayedRouteEstimate", "no actual routes returned");
                return;
            }

            // 获取之前的预估结果进行对比
            compareWithPreviousEstimate(cityId, useTime, userOrderId, positions, actualRoutes, originalCallTime, actualCallTime);

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.processDelayedRouteEstimate error", e);
        }
    }

    /**
     * 与之前的预估结果进行对比
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @param actualRoutes 实际路径结果
     * @param originalCallTime 原始调用时间
     * @param actualCallTime 实际调用时间
     */
    private void compareWithPreviousEstimate(Integer cityId, Date useTime, String userOrderId,
                                           List<Position> positions, List<Route> actualRoutes,
                                           Date originalCallTime, Date actualCallTime) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            // 获取之前的预估结果（这里可以从缓存或数据库中获取）
            List<Route> previousEstimateRoutes = getPreviousEstimateRoutes(cityId, useTime, userOrderId, positions);
            
            if (CollectionUtils.isEmpty(previousEstimateRoutes)) {
                logger.warn("DelayedRouteEstimateConsumer.compareWithPreviousEstimate", "no previous estimate routes found");
                return;
            }

            // 记录监控指标
            for (int i = 0; i < Math.min(positions.size(), Math.min(actualRoutes.size(), previousEstimateRoutes.size())); i++) {
                Position position = positions.get(i);
                Route actualRoute = actualRoutes.get(i);
                Route previousRoute = previousEstimateRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double actualDuration = actualRoute.getDuration();
                Double previousFutureDuration = previousRoute.getDuration();
                Double actualFutureDiff = Math.abs(actualDuration - previousFutureDuration);

                // 记录延迟路径预估监控指标
                RouteMonitorUtil.logDelayedRouteEstimate(cityId, useTime, actualCallTime,
                        startPoint, endPoint, actualDuration, previousFutureDuration, actualFutureDiff, false);

                logger.info("DelayedRouteEstimateConsumer.compareWithPreviousEstimate", 
                        "cityId: {}, position: {}->{}, actualDuration: {}, previousDuration: {}, diff: {}",
                        cityId, startPoint, endPoint, actualDuration, previousFutureDuration, actualFutureDiff);
            }

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.compareWithPreviousEstimate error", e);
        }
    }

    /**
     * 获取之前的预估结果
     * 这里可以从缓存或数据库中获取之前的预估结果
     * 
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @return 之前的预估路径结果
     */
    private List<Route> getPreviousEstimateRoutes(Integer cityId, Date useTime, String userOrderId, List<Position> positions) {
        try {
            // 这里可以实现从缓存或数据库中获取之前的预估结果的逻辑
            // 为了简化，这里直接调用旧接口作为对比基准
            return geoGateway.queryRoutes(cityId, positions, useTime, userOrderId);
        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.getPreviousEstimateRoutes error", e);
            return null;
        }
    }
}
