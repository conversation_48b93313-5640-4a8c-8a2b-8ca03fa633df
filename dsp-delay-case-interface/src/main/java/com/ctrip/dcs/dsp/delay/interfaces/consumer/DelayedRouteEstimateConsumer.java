package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.GeoGatewayImpl;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.RouteApiSwitchConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.RouteMonitorUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Date;
import java.util.List;

/**
 * 延迟路径预估消息消费者
 * 处理延迟消息，在真实用车时间调用路径预估接口获取实际结果
 * 
 * <AUTHOR>
 */
@Component
public class DelayedRouteEstimateConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayedRouteEstimateConsumer.class);

    @Autowired
    private GeoGatewayImpl geoGateway;

    @Autowired
    private RouteApiSwitchConfig routeApiSwitchConfig;

    /**
     * 处理延迟路径预估消息
     *
     * @param message 消息
     */
    @QmqConsumer(prefix = "delayed_route_estimate", consumerGroup = "dsp-delay-case")
    public void handleDelayedRouteEstimate(Message message) {
        try {
            logger.info("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate", "received delayed message");

            // 解析消息数据
            Integer cityId = message.getIntProperty("cityId");
            Date useTime = (Date) message.getProperty("useTime");
            String userOrderId = message.getStringProperty("userOrderId");
            Date originalCallTime = (Date) message.getProperty("callTime");

            String positionsJson = message.getStringProperty("positions");
            List<Position> positions = JsonUtil.fromJson(positionsJson, new TypeReference<List<Position>>() {});

            String originalRoutesJson = message.getStringProperty("originalRoutes");
            List<Route> originalRoutes = JsonUtil.fromJson(originalRoutesJson, new TypeReference<List<Route>>() {});

            if (cityId == null || useTime == null || CollectionUtils.isEmpty(positions) || CollectionUtils.isEmpty(originalRoutes)) {
                logger.warn("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate", "invalid message data");
                return;
            }

            // 处理延迟路径预估，使用消息中的原先结果
            processDelayedRouteEstimate(cityId, useTime, userOrderId, positions, originalRoutes, originalCallTime);

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.handleDelayedRouteEstimate error", e);
        }
    }

    /**
     * 处理延迟路径预估（使用消息中的原先结果，不再重新调用接口）
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @param originalRoutes 原先的路径结果
     * @param originalCallTime 原始调用时间
     */
    private void processDelayedRouteEstimate(Integer cityId, Date useTime, String userOrderId,
                                           List<Position> positions, List<Route> originalRoutes, Date originalCallTime) {
        try {
            Date actualCallTime = new Date();

            logger.info("DelayedRouteEstimateConsumer.processDelayedRouteEstimate",
                "processing delayed estimate, cityId: {}, originalRoutes count: {}", cityId, originalRoutes.size());

            // 现在调用新接口获取实际结果（在真实用车时间）
            List<Route> actualRoutes = geoGateway.queryRoutesNew(cityId, useTime, userOrderId, positions);

            if (CollectionUtils.isEmpty(actualRoutes)) {
                logger.warn("DelayedRouteEstimateConsumer.processDelayedRouteEstimate", "no actual routes returned");
                // 即使没有实际结果，也记录监控数据
                recordDelayedEstimateMonitoring(cityId, useTime, positions, originalRoutes, null, originalCallTime, actualCallTime);
                return;
            }

            // 与原先的结果进行对比
            compareWithOriginalEstimate(cityId, useTime, userOrderId, positions, originalRoutes, actualRoutes, originalCallTime, actualCallTime);

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.processDelayedRouteEstimate error", e);
            // 记录异常情况的监控
            recordDelayedEstimateMonitoring(cityId, useTime, positions, originalRoutes, null, originalCallTime, new Date());
        }
    }

    /**
     * 与原先的预估结果进行对比
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @param originalRoutes 原先的路径结果
     * @param actualRoutes 实际路径结果
     * @param originalCallTime 原始调用时间
     * @param actualCallTime 实际调用时间
     */
    private void compareWithOriginalEstimate(Integer cityId, Date useTime, String userOrderId,
                                           List<Position> positions, List<Route> originalRoutes, List<Route> actualRoutes,
                                           Date originalCallTime, Date actualCallTime) {
        try {
            recordDelayedEstimateMonitoring(cityId, useTime, positions, originalRoutes, actualRoutes, originalCallTime, actualCallTime);

            logger.info("DelayedRouteEstimateConsumer.compareWithOriginalEstimate",
                    "completed comparison for cityId: {}, originalRoutes: {}, actualRoutes: {}",
                    cityId, originalRoutes.size(), actualRoutes.size());

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.compareWithOriginalEstimate error", e);
        }
    }

    /**
     * 记录延迟预估监控数据
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param positions 位置列表
     * @param originalRoutes 原先的路径结果
     * @param actualRoutes 实际路径结果（可能为null）
     * @param originalCallTime 原始调用时间
     * @param actualCallTime 实际调用时间
     */
    private void recordDelayedEstimateMonitoring(Integer cityId, Date useTime, List<Position> positions,
                                               List<Route> originalRoutes, List<Route> actualRoutes,
                                               Date originalCallTime, Date actualCallTime) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            int minSize = positions.size();
            if (actualRoutes != null) {
                minSize = Math.min(minSize, Math.min(originalRoutes.size(), actualRoutes.size()));
            } else {
                minSize = Math.min(minSize, originalRoutes.size());
            }

            // 记录监控指标
            for (int i = 0; i < minSize; i++) {
                Position position = positions.get(i);
                Route originalRoute = originalRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double originalDuration = originalRoute.getDuration();
                Double actualDuration = actualRoutes != null && i < actualRoutes.size() ? actualRoutes.get(i).getDuration() : null;
                Double diff = actualDuration != null ? Math.abs(actualDuration - originalDuration) : 0.0;

                // 记录延迟路径预估监控指标
                RouteMonitorUtil.logDelayedRouteEstimate(cityId, useTime, actualCallTime,
                        startPoint, endPoint, actualDuration != null ? actualDuration : 0.0,
                        originalDuration, diff, false);

                logger.info("DelayedRouteEstimateConsumer.recordDelayedEstimateMonitoring",
                        "cityId: {}, position: {}->{}, originalDuration: {}, actualDuration: {}, diff: {}",
                        cityId, startPoint, endPoint, originalDuration, actualDuration, diff);
            }

        } catch (Exception e) {
            logger.error("DelayedRouteEstimateConsumer.recordDelayedEstimateMonitoring error", e);
        }
    }


}
