package com.ctrip.dcs.dsp.delay.infrastructure.ratelimit;

import com.ctrip.dcs.dsp.delay.ratelimit.RedisRateLimiterConfig;
import credis.java.client.CacheProvider;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Redis限流器单元测试
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class RedisRateLimiterImplTest {

    @Mock
    private CacheProvider cacheProvider;

    @Mock
    private RedisRateLimiterConfig config;

    @InjectMocks
    private RedisRateLimiterImpl rateLimiter;

    private static final String TEST_KEY = "test_key";

    @Before
    public void setUp() {
        // 设置默认配置
        when(config.isEnabled()).thenReturn(true);
        when(config.getBucketCapacity()).thenReturn(100);
        when(config.getTokensPerSecond()).thenReturn(10);
        when(config.getKeyExpireSeconds()).thenReturn(300);
        when(config.getDefaultTimeoutMillis()).thenReturn(100L);
        when(config.isPassOnException()).thenReturn(true);
        when(config.getMinTokenRequest()).thenReturn(1);
        when(config.getMaxTokenRequest()).thenReturn(100);
    }

    @Test
    public void testTryAcquire_WhenDisabled_ShouldAlwaysPass() {
        // 限流器禁用时应该总是通过
        when(config.isEnabled()).thenReturn(false);
        
        assertTrue(rateLimiter.tryAcquire(TEST_KEY));
        assertTrue(rateLimiter.tryAcquire(TEST_KEY, 10));
        
        // 不应该调用Redis
        verify(cacheProvider, never()).get(anyString());
    }

    @Test
    public void testTryAcquire_WithInvalidKey_ShouldReturnBasedOnConfig() {
        // 无效key应该根据配置返回
        when(config.isPassOnException()).thenReturn(true);
        assertTrue(rateLimiter.tryAcquire(null));
        assertTrue(rateLimiter.tryAcquire(""));
        
        when(config.isPassOnException()).thenReturn(false);
        assertFalse(rateLimiter.tryAcquire(null));
        assertFalse(rateLimiter.tryAcquire(""));
    }

    @Test
    public void testTryAcquire_WithInvalidPermits_ShouldReturnFalse() {
        // 无效的令牌数应该返回false
        assertFalse(rateLimiter.tryAcquire(TEST_KEY, 0));
        assertFalse(rateLimiter.tryAcquire(TEST_KEY, -1));
        assertFalse(rateLimiter.tryAcquire(TEST_KEY, 101)); // 超过最大值
    }

    @Test
    public void testTryAcquire_FirstAccess_ShouldInitializeFullBucket() throws Exception {
        // 首次访问应该初始化为满桶
        when(cacheProvider.get(anyString())).thenReturn(null);
        when(cacheProvider.setex(anyString(), anyInt(),anyString())).thenReturn(true);
        
        assertTrue(rateLimiter.tryAcquire(TEST_KEY, 10));
        
        // 应该设置两个key：tokens和timestamp
        verify(cacheProvider, times(2)).setex(anyString(), eq(300),anyString());
    }

    @Test
    public void testTryAcquire_WithSufficientTokens_ShouldSucceed() throws Exception {
        // 有足够令牌时应该成功
        when(cacheProvider.get(contains("tokens"))).thenReturn("50.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(System.currentTimeMillis()));
        when(cacheProvider.setex(anyString(),  anyInt(),anyString())).thenReturn(true);
        
        assertTrue(rateLimiter.tryAcquire(TEST_KEY, 10));
        
        // 应该更新令牌数
        verify(cacheProvider, times(2)).setex(anyString(), eq(300), anyString());
    }

    @Test
    public void testTryAcquire_WithInsufficientTokens_ShouldFail() throws Exception {
        // 令牌不足时应该失败
        when(cacheProvider.get(contains("tokens"))).thenReturn("5.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(System.currentTimeMillis()));
        when(cacheProvider.setex(anyString(),anyInt(), anyString())).thenReturn(true);
        
        assertFalse(rateLimiter.tryAcquire(TEST_KEY, 10));
        
        // 仍然应该更新状态
        verify(cacheProvider, times(2)).setex(anyString(),eq(300), anyString());
    }

    @Test
    public void testTryAcquire_WithTokenRefill_ShouldWork() throws Exception {
        // 测试令牌补充机制
        long pastTime = System.currentTimeMillis() - 5000; // 5秒前
        
        when(cacheProvider.get(contains("tokens"))).thenReturn("10.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(pastTime));
        when(cacheProvider.setex(anyString(),anyInt(), anyString())).thenReturn(true);
        
        // 10个初始令牌 + 5秒 * 10令牌/秒 = 60个令牌
        assertTrue(rateLimiter.tryAcquire(TEST_KEY, 30));
    }

    @Test
    public void testTryAcquire_WithException_ShouldReturnBasedOnConfig() throws Exception {
        // 异常时应该根据配置返回
        when(cacheProvider.get(anyString())).thenThrow(new RuntimeException("Redis error"));
        
        when(config.isPassOnException()).thenReturn(true);
        assertTrue(rateLimiter.tryAcquire(TEST_KEY));
        
        when(config.isPassOnException()).thenReturn(false);
        assertFalse(rateLimiter.tryAcquire(TEST_KEY));
    }

    @Test
    public void testGetAvailableTokens_FirstAccess_ShouldReturnCapacity() {
        // 首次访问应该返回容量
        when(cacheProvider.get(anyString())).thenReturn(null);
        
        assertEquals(100, rateLimiter.getAvailableTokens(TEST_KEY));
    }

    @Test
    public void testGetAvailableTokens_WithExistingTokens_ShouldReturnCorrectAmount() throws Exception {
        // 有现存令牌时应该返回正确数量
        when(cacheProvider.get(contains("tokens"))).thenReturn("30.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(System.currentTimeMillis()));
        
        assertEquals(30, rateLimiter.getAvailableTokens(TEST_KEY));
    }

    @Test
    public void testGetAvailableTokens_WithTokenRefill_ShouldCalculateCorrectly() throws Exception {
        // 测试令牌补充后的计算
        long pastTime = System.currentTimeMillis() - 3000; // 3秒前
        
        when(cacheProvider.get(contains("tokens"))).thenReturn("20.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(pastTime));
        
        // 20个初始令牌 + 3秒 * 10令牌/秒 = 50个令牌
        assertEquals(50, rateLimiter.getAvailableTokens(TEST_KEY));
    }

    @Test
    public void testGetAvailableTokens_ExceedingCapacity_ShouldReturnCapacity() throws Exception {
        // 超过容量时应该返回容量值
        long pastTime = System.currentTimeMillis() - 20000; // 20秒前
        
        when(cacheProvider.get(contains("tokens"))).thenReturn("80.0");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(pastTime));
        
        // 80个初始令牌 + 20秒 * 10令牌/秒 = 280个令牌，但容量只有100
        assertEquals(100, rateLimiter.getAvailableTokens(TEST_KEY));
    }

    @Test
    public void testReset_ShouldDeleteKeys() throws Exception {
        // 重置应该删除相关key
        when(cacheProvider.del(anyString())).thenReturn(true);
        
        rateLimiter.reset(TEST_KEY);
        
        // 应该删除两个key：tokens和timestamp
        verify(cacheProvider, times(2)).del(anyString());
    }

    @Test
    public void testReset_WithException_ShouldNotThrow() throws Exception {
        // 重置时异常不应该抛出
        when(cacheProvider.del(anyString())).thenThrow(new RuntimeException("Redis error"));
        
        // 不应该抛出异常
        rateLimiter.reset(TEST_KEY);
    }

    @Test
    public void testIsEnabled_ShouldReturnCorrectStatus() {
        // Redis不可用时应该返回false
        rateLimiter = new RedisRateLimiterImpl();
        when(config.isEnabled()).thenReturn(true);
        assertFalse(rateLimiter.isEnabled());
        
        // 配置禁用时应该返回false
        when(config.isEnabled()).thenReturn(false);
        assertFalse(rateLimiter.isEnabled());
        
        // 都可用时应该返回true
        rateLimiter = new RedisRateLimiterImpl();
        when(config.isEnabled()).thenReturn(true);
        // 注入cacheProvider后才能返回true
    }

    @Test
    public void testTryAcquire_WithTimeout_ShouldRetry() throws Exception {
        // 带超时的获取应该重试
        when(cacheProvider.get(contains("tokens")))
                .thenReturn("0.0")  // 第一次没有令牌
                .thenReturn("10.0"); // 第二次有令牌
        when(cacheProvider.get(contains("timestamp")))
                .thenReturn(String.valueOf(System.currentTimeMillis()));
        when(cacheProvider.setex(anyString(), anyInt(),anyString())).thenReturn(true);
        
        assertTrue(rateLimiter.tryAcquire(TEST_KEY, 5, 200));
    }

    @Test
    public void testTryAcquire_WithCorruptedData_ShouldResetAndReturn() throws Exception {
        // 数据损坏时应该重置并根据配置返回
        when(cacheProvider.get(contains("tokens"))).thenReturn("invalid");
        when(cacheProvider.get(contains("timestamp"))).thenReturn(String.valueOf(System.currentTimeMillis()));
        when(config.isPassOnException()).thenReturn(true);
        
        assertTrue(rateLimiter.tryAcquire(TEST_KEY));
        
        // 应该尝试重置
        verify(cacheProvider, times(2)).del(anyString());
    }
}
