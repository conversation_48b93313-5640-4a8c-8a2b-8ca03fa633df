package com.ctrip.dcs.dsp.delay.infrastructure.proxy;

import com.ctrip.dcs.dsp.delay.infrastructure.http.InventoryProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.BStatus;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {HttpUtil.class})
@PowerMockIgnore("javax.net.ssl.*")
public class InventoryProxyTest {

    @InjectMocks
    private InventoryProxy proxy;

    @Mock
    private HttpUrlQConfig httpUrlQConfig;

    @Test
    public void testUnfrozen() {

        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(httpUrlQConfig.getUnfrozenUrl()).thenReturn("http://xxx.com");
        QunarResResult result = new QunarResResult();
        BStatus status = new BStatus();
        status.setCode(0);
        result.setBstatus(status);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        proxy.unfrozen("1", "1");
        Mockito.verify(httpUrlQConfig, Mockito.times(1)).getUnfrozenUrl();
    }

    @Test
    public void testBatchFrozen() {

        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(httpUrlQConfig.getBatchFrozenUrl()).thenReturn("http://xxx.com");
        QunarResResult result = new QunarResResult();
        BStatus status = new BStatus();
        status.setCode(0);
        result.setBstatus(status);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        FrozenDTO frozen = new FrozenDTO("1", "2");
        proxy.batchFrozen(Lists.newArrayList(frozen));
        Mockito.verify(httpUrlQConfig, Mockito.times(1)).getBatchFrozenUrl();
    }
}
