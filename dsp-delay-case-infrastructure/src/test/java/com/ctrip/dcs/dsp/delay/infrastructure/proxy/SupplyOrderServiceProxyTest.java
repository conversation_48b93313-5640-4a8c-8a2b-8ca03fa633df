package com.ctrip.dcs.dsp.delay.infrastructure.proxy;

import com.ctrip.dcs.dsp.delay.infrastructure.dto.RedispatchResultDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.SupplyOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.BStatus;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.model.OrderTakenResult;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.UserChoicePackageService;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {HttpUtil.class, MetricsUtil.class, MetricsFactory.class})
@PowerMockIgnore("javax.net.ssl.*")
public class SupplyOrderServiceProxyTest {

    @InjectMocks
    private SupplyOrderServiceProxy supplyOrderServiceProxy;

    @Mock
    private HttpUrlQConfig httpUrlQConfig;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private MetricRegistry metricRegistry;

    @Test
    public void testQueryUserChoicePackageService() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QunarResResult<List<UserChoicePackageService>> result = new QunarResResult<>();
        result.setBstatus(BStatus.SUCCESS);
        PowerMockito.when(httpUrlQConfig.getUserChoicePackageServiceUrl()).thenReturn("http://xxx.com");
        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        List<UserChoicePackageService> list = supplyOrderServiceProxy.queryUserChoicePackageService("123");
        Assert.assertNotNull(list);
    }

    @Test
    public void testQueryByDriver() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QunarResResult<Map<String/*orderId*/, SupplyOrder>> result = new QunarResResult<>();
        result.setBstatus(BStatus.SUCCESS);
        PowerMockito.when(httpUrlQConfig.getRelateOrdersUrl()).thenReturn("http://xxx.com");
        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        List<SupplyOrder> list = supplyOrderServiceProxy.queryByDriver(Lists.newArrayList("1", "2"));
        Assert.assertNotNull(list);
    }


    @Test
    public void testQueryUserChoicePackageServiceError() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QunarResResult<List<UserChoicePackageService>> result = new QunarResResult<>();
        PowerMockito.when(httpUrlQConfig.getUserChoicePackageServiceUrl()).thenReturn("http://xxx.com");
        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        List<UserChoicePackageService> list = null;
        try {
            list = supplyOrderServiceProxy.queryUserChoicePackageService("123");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNull(list);
    }

    @Test
    public void testTaken() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QunarResResult<OrderTakenResult> result = new QunarResResult<>();
        result.setData(new OrderTakenResult());
        BStatus status = new BStatus();
        status.setCode(0);
        result.setBstatus(status);
        PowerMockito.when(httpUrlQConfig.getOrderTakenUrl()).thenReturn("http://xxx.com");
        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        OrderTakenResult taken = supplyOrderServiceProxy.taken("123", "123", "123", 1);
        Assert.assertNotNull(taken);
    }

    @Test
    public void testRedispatch() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QunarResResult<RedispatchResultDTO> result = new QunarResResult<>();
        result.setData(new RedispatchResultDTO());
        BStatus status = new BStatus();
        status.setCode(0);
        result.setBstatus(status);
        PowerMockito.when(httpUrlQConfig.getRedispatchUrl()).thenReturn("http://xxx.com");
        PowerMockito.mockStatic(HttpUtil.class);
        PowerMockito.when(HttpUtil.post(Mockito.any(HttpUtil.HttpPostParam.class))).thenReturn(result);
        RedispatchResultDTO redispatch = supplyOrderServiceProxy.redispatch("123");
        Assert.assertNotNull(redispatch);
    }
}
