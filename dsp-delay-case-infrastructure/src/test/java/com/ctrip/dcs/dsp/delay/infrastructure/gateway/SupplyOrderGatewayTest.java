package com.ctrip.dcs.dsp.delay.infrastructure.gateway;

import com.ctrip.dcs.dsp.delay.enums.XSkuCategoryCode;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.RedispatchResultDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.SupplyOrderGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.http.SupplyOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.model.OrderTakenResult;
import com.ctrip.dcs.dsp.delay.model.UserChoicePackageService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Set;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SupplyOrderGatewayTest {

    @InjectMocks
    private SupplyOrderGatewayImpl supplyOrderGateway;

    @Mock
    private SupplyOrderServiceProxy supplyOrderServiceProxy;

    @Test
    public void testTaken() {
        OrderTakenResult result = new OrderTakenResult();
        PowerMockito.when(supplyOrderServiceProxy.taken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(result);
        Integer res = supplyOrderGateway.taken("123", "1", "1", 1);
        Assert.assertEquals(500, res.intValue());
    }

    @Test
    public void testRedispatch() {
        RedispatchResultDTO result = new RedispatchResultDTO();
        PowerMockito.when(supplyOrderServiceProxy.redispatch(Mockito.anyString())).thenReturn(result);
        supplyOrderGateway.redispatch("123");
        Mockito.verify(supplyOrderServiceProxy, Mockito.times(2)).redispatch(Mockito.any());
    }

    @Test
    public void testQueryOrderXSkuCode() {
        UserChoicePackageService s = new UserChoicePackageService();
        s.setCategoryCode(XSkuCategoryCode.PICK_UP_CARD.getCode());
        PowerMockito.when(supplyOrderServiceProxy.queryUserChoicePackageService(Mockito.anyString())).thenReturn(Lists.newArrayList(s));
        Set<String> set = supplyOrderGateway.queryOrderXSkuCode("123");
        Assert.assertTrue(set.contains(XSkuCategoryCode.PICK_UP_CARD.getCode()));
    }
}
