package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.InventoryGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.http.InventoryProxy
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenResultDTO
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TransportInventoryServiceProxy
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.Frozen
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryResponseType
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.Lists
import io.dropwizard.metrics5.MetricRegistry
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])
class InventoryGatewayImplTest extends Specification {

    def inventoryProxy = Mock(InventoryProxy)

    def config = Mock(DelayDspCommonQConfig)
    def transportInventoryServiceProxy = Mock(TransportInventoryServiceProxy)

    def gateway = new InventoryGatewayImpl(inventoryProxy: inventoryProxy, delayDspCommonQConfig: config, transportInventoryServiceProxy: transportInventoryServiceProxy)

    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }

    @Unroll
    def "unfrozen"() {

        given: "Mock数据"
        def order = new DelayDspOrder()
        def driver = new Driver()

        when: "执行校验方法"
        gateway.unfrozen(null, driver)
        gateway.unfrozen(order, null)
        gateway.unfrozen(order, driver)

        then: "验证校验结果"
        true
    }

    @Unroll
    def "batchFrozen"() {

        given: "Mock数据"
        def order = new DelayDspOrder()
        def driver = new Driver()
        def frozen1 = new Frozen(new DelayDspOrder(orderId: "1"), driver)
        def frozen2 = new Frozen(new DelayDspOrder(orderId: "2"), driver)
        def frozen3 = new Frozen(new DelayDspOrder(orderId: "3"), driver)
        config.getBatchFrozenSize() >> 1
        inventoryProxy.batchFrozen(_) >> Lists.newArrayList(new FrozenResultDTO(orderId: "1", frozen: 1)) >> Lists.newArrayList(new FrozenResultDTO(orderId: "2", frozen: 1)) >> Lists.newArrayList(new FrozenResultDTO(orderId: "3", frozen: 0))

        when: "执行校验方法"
        gateway.batchFrozen(null)
        gateway.batchFrozen(Lists.newArrayList(frozen1, frozen2, frozen3))

        then: "验证校验结果"
        frozen1.getFrozen() == 1
        frozen2.getFrozen() == 1
        frozen3.getFrozen() == 0
    }

    @Unroll
    def "checkInventoryConflict"() {

        given: "Mock数据"
        SupplyOrder order = new SupplyOrder()
        order.setSysExpectBookTime(new Date())
        order.setPredicServiceStopTime(new Date())
        order.setOrderId("1")

        transportInventoryServiceProxy.useVirtualInventory(_) >> buildUseVirtualInventoryResponseType()
        when: "执行校验方法"
        def finalRes = gateway.checkInventoryConflict(Lists.newArrayList("1"),order)

        then: "验证校验结果"
        finalRes == "true"
    }

    @Unroll
    def "checkInventoryConflict1"() {

        given: "Mock数据"
        SupplyOrder order = new SupplyOrder()
        order.setSysExpectBookTime(new Date())
        order.setPredicServiceStopTime(new Date())
        order.setOrderId("1")

        transportInventoryServiceProxy.useVirtualInventory(_) >> null
        when: "执行校验方法"
        def finalRes = gateway.checkInventoryConflict(Lists.newArrayList("1"),order)

        then: "验证校验结果"
        finalRes == null
    }

    UseVirtualInventoryResponseType buildUseVirtualInventoryResponseType(){
        UseVirtualInventoryResponseType useVirtualInventoryResponseType = new UseVirtualInventoryResponseType()
        useVirtualInventoryResponseType.setDriverId("true")
        ResponseResult responseResult = new ResponseResult()
        responseResult.setSuccess(true)
        useVirtualInventoryResponseType.setResponseResult(responseResult)
        return useVirtualInventoryResponseType
    }

}
