package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.CarConfigGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.http.CarConfigServiceProxy
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy
import com.ctrip.dcs.dsp.delay.util.JsonUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class CarConfigGatewayImplSpec extends Specification {

    def carConfigServiceProxy = Mock(CarConfigServiceProxy)

    def tRocksProviderProxy = Mock(TRocksProviderProxy)

    def gateway = new CarConfigGatewayImpl("carConfigServiceProxy": carConfigServiceProxy, "tRocksProviderProxy": tRocksProviderProxy)

    @Unroll
    def "query"() {

        given: "Mock数据"
        tRocksProviderProxy.get(_) >> v
        carConfigServiceProxy.query(_, _) >> config

        when: "执行校验方法"
        def res = gateway.query(CarConfigCode.DELAY_DSP_SUB_SKU.code, CarConfigCode.DELAY_DSP_SUB_SKU.version)

        then: "验证校验结果"
        res.uniqCode == config.uniqCode

        where:
        v      || config
        null   || new CarConfig()
        json() || new CarConfig("uniqCode": CarConfigCode.DELAY_DSP_SUB_SKU.code)
    }

    def json() {
        CarConfig config = new CarConfig("uniqCode": CarConfigCode.DELAY_DSP_SUB_SKU.code)
        return JsonUtil.toJson(config)
    }
}
