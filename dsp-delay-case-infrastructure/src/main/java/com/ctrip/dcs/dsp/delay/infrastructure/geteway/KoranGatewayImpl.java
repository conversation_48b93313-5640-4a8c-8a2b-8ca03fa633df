package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.gateway.KoranGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.http.KoranServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class KoranGatewayImpl implements KoranGateway {

    @Autowired
    private KoranServiceProxy koranServiceProxy;

    @Override
    public void initDspContext(String duid) {
        koranServiceProxy.initDspContext(duid);
    }
}
