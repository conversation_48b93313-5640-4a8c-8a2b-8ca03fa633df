package com.ctrip.dcs.dsp.delay.infrastructure.ratelimit;

import com.ctrip.dcs.dsp.delay.ratelimit.RedisRateLimiter;
import com.ctrip.dcs.dsp.delay.ratelimit.RedisRateLimiterConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import credis.java.client.CacheProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * Redis限流器实现
 * 基于令牌桶算法，不使用Lua脚本
 * <AUTHOR>
 */
@Component
public class RedisRateLimiterImpl implements RedisRateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterImpl.class);

    private static final String KEY_PREFIX = "rate_limiter:";
    private static final String TOKENS_KEY_SUFFIX = ":tokens";
    private static final String TIMESTAMP_KEY_SUFFIX = ":timestamp";

    @Qualifier("redisCacheProvider")
    @Autowired(required = false)
    private CacheProvider cacheProvider;

    @Autowired
    private RedisRateLimiterConfig config;

    @PostConstruct
    public void init() {
        if (cacheProvider == null) {
            logger.warn("Redis CacheProvider not found, rate limiter will be disabled");
        }
        logger.info("RedisRateLimiter initialized with config: enabled={}, capacity={}, rate={}/s", 
                    config.isEnabled(), config.getBucketCapacity(), config.getTokensPerSecond());
    }

    @Override
    public boolean tryAcquire(String key) {
        return tryAcquire(key, 1);
    }

    @Override
    public boolean tryAcquire(String key, int permits) {
        return tryAcquire(key, permits, config.getDefaultTimeoutMillis());
    }

    @Override
    public boolean tryAcquire(String key, int permits, long timeoutMillis) {
        // 参数校验
        if (key == null || key.isEmpty()) {
            logger.warn("Invalid key for rate limiter: null or empty");
            return config.isPassOnException();
        }

        if (permits < config.getMinTokenRequest() || permits > config.getMaxTokenRequest()) {
            logger.warn("Invalid permits: {}, should be between {} and {}", 
                       permits, config.getMinTokenRequest(), config.getMaxTokenRequest());
            return false;
        }

        // 检查是否启用
        if (!isEnabled()) {
            return true;
        }

        // 检查Redis是否可用
        if (cacheProvider == null) {
            logger.warn("Redis not available, rate limiter disabled");
            return config.isPassOnException();
        }

        long startTime = System.currentTimeMillis();
        long endTime = startTime + timeoutMillis;

        while (System.currentTimeMillis() < endTime) {
            try {
                if (doTryAcquire(key, permits)) {
                    long costTime = System.currentTimeMillis() - startTime;
                    if (costTime > 10) {
                        logger.info("Acquired {} tokens for key: {} in {}ms", permits, key, costTime);
                    }
                    return true;
                }

                // 如果没有超时时间，直接返回
                if (timeoutMillis <= 0) {
                    return false;
                }

                // 短暂休眠后重试
                long remainingTime = endTime - System.currentTimeMillis();
                if (remainingTime > 0) {
                    Thread.sleep(Math.min(10, remainingTime));
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Thread interrupted while waiting for tokens: {}", key);
                return false;
            } catch (Exception e) {
                logger.error("Error acquiring tokens for key: {}", key, e);
                return config.isPassOnException();
            }
        }

        logger.debug("Timeout waiting for {} tokens for key: {}", permits, key);
        return false;
    }

    private boolean doTryAcquire(String key, int permits) {
        String tokensKey = KEY_PREFIX + key + TOKENS_KEY_SUFFIX;
        String timestampKey = KEY_PREFIX + key + TIMESTAMP_KEY_SUFFIX;

        try {
            long now = System.currentTimeMillis();
            
            // 获取当前令牌数和上次更新时间
            String tokensStr = cacheProvider.get(tokensKey);
            String timestampStr = cacheProvider.get(timestampKey);

            double currentTokens;
            long lastRefillTime;

            if (tokensStr == null || timestampStr == null) {
                // 首次访问，初始化为满桶
                currentTokens = config.getBucketCapacity();
                lastRefillTime = now;
            } else {
                currentTokens = Double.parseDouble(tokensStr);
                lastRefillTime = Long.parseLong(timestampStr);
            }

            // 计算时间差并补充令牌
            long timeDelta = now - lastRefillTime;
            if (timeDelta > 0) {
                double tokensToAdd = (timeDelta / 1000.0) * config.getTokensPerSecond();
                currentTokens = Math.min(currentTokens + tokensToAdd, config.getBucketCapacity());
            }

            // 检查令牌是否足够
            if (currentTokens < permits) {
                // 令牌不足，更新状态但不消费
                updateTokens(tokensKey, timestampKey, currentTokens, now);
                return false;
            }

            // 消费令牌
            currentTokens -= permits;
            updateTokens(tokensKey, timestampKey, currentTokens, now);
            
            logger.debug("Consumed {} tokens for key: {}, remaining: {}", permits, key, currentTokens);
            return true;

        } catch (NumberFormatException e) {
            logger.error("Invalid token data for key: {}", key, e);
            // 数据损坏，重置
            reset(key);
            return config.isPassOnException();
        } catch (Exception e) {
            logger.error("Error in doTryAcquire for key: {}", key, e);
            return config.isPassOnException();
        }
    }

    private void updateTokens(String tokensKey, String timestampKey, double tokens, long timestamp) {
        try {
            cacheProvider.setex(tokensKey,config.getKeyExpireSeconds(), String.valueOf(tokens));
            cacheProvider.setex(timestampKey, config.getKeyExpireSeconds(),String.valueOf(timestamp));
        } catch (Exception e) {
            logger.error("Error updating tokens: tokensKey={}, timestampKey={}", tokensKey, timestampKey, e);
        }
    }

    @Override
    public long getAvailableTokens(String key) {
        if (!isEnabled() || cacheProvider == null) {
            return config.getBucketCapacity();
        }

        String tokensKey = KEY_PREFIX + key + TOKENS_KEY_SUFFIX;
        String timestampKey = KEY_PREFIX + key + TIMESTAMP_KEY_SUFFIX;

        try {
            String tokensStr = cacheProvider.get(tokensKey);
            String timestampStr = cacheProvider.get(timestampKey);

            if (tokensStr == null || timestampStr == null) {
                return config.getBucketCapacity();
            }

            double currentTokens = Double.parseDouble(tokensStr);
            long lastRefillTime = Long.parseLong(timestampStr);
            long now = System.currentTimeMillis();

            // 计算当前应有的令牌数
            long timeDelta = now - lastRefillTime;
            if (timeDelta > 0) {
                double tokensToAdd = (timeDelta / 1000.0) * config.getTokensPerSecond();
                currentTokens = Math.min(currentTokens + tokensToAdd, config.getBucketCapacity());
            }

            return (long) currentTokens;

        } catch (Exception e) {
            logger.error("Error getting available tokens for key: {}", key, e);
            return 0;
        }
    }

    @Override
    public void reset(String key) {
        if (cacheProvider == null) {
            return;
        }

        String tokensKey = KEY_PREFIX + key + TOKENS_KEY_SUFFIX;
        String timestampKey = KEY_PREFIX + key + TIMESTAMP_KEY_SUFFIX;

        try {
            cacheProvider.del(tokensKey);
            cacheProvider.del(timestampKey);
            logger.info("Reset rate limiter for key: {}", key);
        } catch (Exception e) {
            logger.error("Error resetting rate limiter for key: {}", key, e);
        }
    }

    @Override
    public boolean isEnabled() {
        return config.isEnabled() && cacheProvider != null;
    }
}
