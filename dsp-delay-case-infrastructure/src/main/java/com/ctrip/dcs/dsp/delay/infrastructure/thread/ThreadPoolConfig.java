package com.ctrip.dcs.dsp.delay.infrastructure.thread;

import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {

    @Bean("driverThreadPool")
    public ExecutorService getDriverThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(4)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-thread-")
                .build();
    }

    @Bean("takenThreadPool")
    public ExecutorService getTakenThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-taken-thread-")
                .build();
    }

    @Bean("redispatchThreadPool")
    public ExecutorService getRedispatchThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-redispatch-thread-")
                .build();
    }
}
