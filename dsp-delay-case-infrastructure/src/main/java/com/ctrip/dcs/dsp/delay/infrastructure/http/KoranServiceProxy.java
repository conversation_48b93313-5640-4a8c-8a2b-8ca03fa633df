package com.ctrip.dcs.dsp.delay.infrastructure.http;

import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Maps;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class KoranServiceProxy {

    private static final Logger logger = LoggerFactory.getLogger(KoranServiceProxy.class);

    @Autowired
    private HttpUrlQConfig httpUrlQConfig;

    /**
     * 初始化Q侧派单上下文
     * @param duid
     */
    public void initDspContext(String duid) {
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("duid", duid);
            HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                    .withUrl(httpUrlQConfig.getInitDspContextUrl())
                    .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                    .withParam(params)
                    .withTypeReference(new TypeReference<QunarResResult<String>>() {})
                    .build();
            QunarResResult<String> result = HttpUtil.post(httpPostParam);
            if (result == null || !result.success()) {
                throw new BizException("init dsp context error!");
            }
        } catch (Exception e) {
            MetricsUtil.recordValue("init.dsp.context.http.error", 1);
            logger.error(e);
        }
    }
}
