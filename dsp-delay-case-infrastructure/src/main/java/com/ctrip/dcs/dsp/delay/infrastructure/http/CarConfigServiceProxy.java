package com.ctrip.dcs.dsp.delay.infrastructure.http;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.clogging.MetricsFactory;
import com.google.common.collect.Maps;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class CarConfigServiceProxy {

    private static final Logger logger = LoggerFactory.getLogger(CarConfigServiceProxy.class);

    private static final String GROUP = "car.dsp";

    @Autowired
    private HttpUrlQConfig httpUrlQConfig;

    public CarConfig query(String code, String version) {
        try {
            Map<String, String> params = Maps.newHashMap();
            params.put("group", GROUP);
            params.put("dataId", code);
            params.put("version", version);
            HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                    .withUrl(httpUrlQConfig.getCarconfigUrl())
                    .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                    .withParam(params)
                    .withTypeReference(new TypeReference<CarConfig>() {})
                    .build();
            return HttpUtil.post(httpPostParam);
        } catch (Exception e) {
            MetricsUtil.recordValue("query.car.config.http.error", 1);
            logger.error(e);
        }
        return null;
    }
}
