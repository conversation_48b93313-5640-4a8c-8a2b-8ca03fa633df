package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.RedispatchResultDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.SupplyOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.model.OrderTakenResult;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.UserChoicePackageService;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SupplyOrderGatewayImpl implements SupplyOrderGateway {

    private static final Logger logger = LoggerFactory.getLogger(SupplyOrderGatewayImpl.class);

    @Autowired
    private SupplyOrderServiceProxy supplyOrderServiceProxy;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private SelfDispatcherOrderGateway dispatcherOrderGateway;

    @Override
    public SupplyOrder query(String orderId) {
        return supplyOrderServiceProxy.query(orderId);
    }

    @Override
    public List<SupplyOrder> queryByDriver(List<String> driverIds) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        return supplyOrderServiceProxy.queryByDriver(driverIds);
    }

    @Override
    public List<SupplyOrder> queryByDriver(List<String> driverIds, Date forStartBookTime, Date forEndBookTime, Date backStartBookTime, Date backEndBookTime) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        return supplyOrderServiceProxy.queryByDriver(driverIds, forStartBookTime, forEndBookTime, backStartBookTime, backEndBookTime);
    }


    @Override
    public Integer taken(String orderId, String driverId, String duid, Integer transportGroupId) {
        try {
            OrderTakenResult result = supplyOrderServiceProxy.taken(orderId, driverId, duid, transportGroupId);
            Integer code = Optional.ofNullable(result).map(OrderTakenResult::getCode).orElse(OrderTakenCode.ERROR.getCode());
            logger.info("order.taken", "order id:{}, taken code:{}", orderId, code);
            if (!Objects.equals(code, OrderTakenCode.SUCCESS.getCode())) {
                throw new BizException("taken order error!", "order id:" + orderId);
            }
            return code;
        } catch (Exception e) {
            logger.warn(e);
            try {
                OrderTakenResult result = supplyOrderServiceProxy.taken(orderId, driverId, duid, transportGroupId);
                return Optional.ofNullable(result).map(OrderTakenResult::getResultCode).orElse(OrderTakenCode.ERROR.getCode());
            } catch (Exception ex) {
                logger.error(ex);
            }
        }
        return OrderTakenCode.ERROR.getCode();
    }

    @Override
    public void redispatch(String mainOrderId) {
        try {
            RedispatchResultDTO result = supplyOrderServiceProxy.redispatch(mainOrderId);
            Integer code = Optional.ofNullable(result).map(RedispatchResultDTO::getCode).orElse(RedispatchResultDTO.ERROR_CODE);
            logger.info("order.redispatch", "order id:{}, redispatch code:{}", mainOrderId, code);
            if (!Objects.equals(code, RedispatchResultDTO.SUCCESS_CODE)) {
                throw new BizException("redispatch order error!", "order id:" + mainOrderId);
            }
        } catch (Exception e) {
            logger.warn(e);
            try {
                logger.info("order.redispatch.retry", "order id:{}", mainOrderId);
                supplyOrderServiceProxy.redispatch(mainOrderId);
            } catch (Exception e1) {
                logger.error(e1);
            }
        }
    }

    @Override
    public Set<String> queryOrderXSkuCode(String mainOrderId) {
        List<UserChoicePackageService> list = supplyOrderServiceProxy.queryUserChoicePackageService(mainOrderId);
        return list.stream().map(UserChoicePackageService::getCategoryCode).collect(Collectors.toSet());
    }
}
