package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelRequestType;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelResponseType;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.Top20CityConfig;
import com.ctrip.dcs.dsp.delay.qconfig.PeakHourConfig;
import com.ctrip.dcs.dsp.delay.qconfig.RouteApiSwitchConfig;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.util.RouteMonitorUtil;
import com.ctrip.dcs.location.application.service.interfaces.dto.EstimateParameter;
import com.ctrip.dcs.location.application.service.interfaces.dto.EstimateResultDTO;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.geo.interfaces.dto.BaseLatLngPairDTO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.ctrip.model.QueryEstimateRouteWithBufferTimeRequestType;
import com.ctrip.model.QueryEstimateRouteWithBufferTimeResponseType;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {

    private static final Logger logger = LoggerFactory.getLogger(GeoGatewayImpl.class);

    @Autowired
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Autowired
    private TRocksProviderProxy trocksProviderProxy;

    @Autowired
    private Cache<String, String> caffeineCache;

    @Autowired
    private LbsRateLimiter rateLimiter;

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Autowired
    private Top20CityConfig top20CityConfig;

    @Autowired
    private PeakHourConfig peakHourConfig;

    @Autowired
    private RouteApiSwitchConfig routeApiSwitchConfig;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private ExecutorService driverThreadPool;

    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions) {
        return queryRoutes(cityId, positions, new Date(), null);
    }

    /**
     * 智能路径查询方法
     *
     * @param cityId 城市ID
     * @param positions 位置列表
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @return 路径列表
     */
    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions, Date useTime, String userOrderId) {
        if (CollectionUtils.isEmpty(positions)) {
            return Lists.newArrayList();
        }

        // 判断是否应该使用新接口
        boolean shouldUseNewApi = shouldUseNewApi(cityId, useTime);
        boolean shouldDirectSwitch = routeApiSwitchConfig.shouldDirectSwitch();
        boolean shouldAsyncMonitor = routeApiSwitchConfig.shouldEnableAsyncMonitor();

        // 记录接口切换监控
        if (routeApiSwitchConfig.shouldEnableMonitor()) {
            boolean isTop20City = top20CityConfig.isTop20City(cityId);
            boolean isPeakHour = peakHourConfig.isPeakHour(cityId, useTime);
            String switchMode = shouldDirectSwitch ? "direct" : "async";
            String apiUsed = shouldUseNewApi && shouldDirectSwitch ? "new" : "old";
            RouteMonitorUtil.logApiSwitch(cityId, isTop20City, isPeakHour, switchMode, apiUsed);
        }

        List<Route> routes;

        if (shouldUseNewApi && shouldDirectSwitch) {
            // 直接切换到新接口
            routes = queryRoutesWithNewApi(cityId, useTime, userOrderId, positions);
        } else {
            // 使用旧接口
            routes = queryRoutesWithOldApi(cityId, positions);

            // 异步监控模式：同时调用新接口进行对比
            if (shouldUseNewApi && shouldAsyncMonitor) {
                asyncMonitorNewApi(cityId, useTime, userOrderId, positions, routes);
            }
        }

        // 发送延迟消息
        if (routeApiSwitchConfig.shouldEnableDelayMessage() && useTime != null) {
            sendDelayedRouteEstimateMessage(cityId, useTime, userOrderId, positions);
        }

        return routes;
    }

    /**
     * 判断是否应该使用新接口
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @return true-使用新接口，false-使用旧接口
     */
    private boolean shouldUseNewApi(Integer cityId, Date useTime) {
        if (!routeApiSwitchConfig.shouldUseNewApi()) {
            return false;
        }

        // 判断是否为top20城市
        boolean isTop20City = top20CityConfig.isTop20City(cityId);
        if (!isTop20City) {
            return false;
        }

        // 判断是否在高峰期
        boolean isPeakHour = peakHourConfig.isPeakHour(cityId, useTime);
        return isPeakHour;
    }

    /**
     * 使用旧接口查询路径
     *
     * @param cityId 城市ID
     * @param positions 位置列表
     * @return 路径列表
     */
    private List<Route> queryRoutesWithOldApi(Integer cityId, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        List<List<Position>> partition = Lists.partition(positions, 99);
        for (List<Position> list : partition) {
            try {
                double acquire = rateLimiter.acquire();
                logger.info("GeoGatewayImpl.queryRoutesWithOldApi", "get token.time:{}", acquire);
                QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
                QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                routes.addAll(buildRoute(response));
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutesWithOldApi", e);
            }
        }
        return routes;
    }

    /**
     * 使用新接口查询路径
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @return 路径列表
     */
    private List<Route> queryRoutesWithNewApi(Integer cityId, Date useTime, String userOrderId, List<Position> positions) {
        return queryRoutesNew(cityId, useTime, userOrderId, positions);
    }

    public List<Route> queryRoutesNew(Integer cityId, Date useTime, String userOrderId, List<Position> positions) {

        if (CollectionUtils.isEmpty(positions)) {
            return Lists.newArrayList();
        }

        List<EstimateParameter> parallelParameters = Lists.newArrayList();

        AtomicInteger index = new AtomicInteger();
        positions.forEach(position -> {

            QueryEstimateRouteWithBufferTimeRequestType requestType = new QueryEstimateRouteWithBufferTimeRequestType();
            BaseGpsDTO from = new BaseGpsDTO();
            from.setLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            from.setLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            from.setCoordType(position.getFromCoordsys());
            from.setCityId(cityId.longValue());
            requestType.setOrigin(from);
            BaseGpsDTO to = new BaseGpsDTO();
            to.setLatitude(BigDecimal.valueOf(position.getToLatitude()));
            to.setLongitude(BigDecimal.valueOf(position.getToLongitude()));
            to.setCoordType(position.getToCoordsys());
            to.setCityId(cityId.longValue());
            requestType.setDestination(to);
            requestType.setDepartureTime(useTime.getTime());
            requestType.setOrderId(userOrderId);

            ExtendInfoDTO extendInfoDTO = new ExtendInfoDTO();
            extendInfoDTO.setForceGaodeFuture(true);
            requestType.setExtendInfoDTO(extendInfoDTO);

            EstimateParameter estimateParameter = new EstimateParameter();
            estimateParameter.setId(index.getAndIncrement());
            estimateParameter.setSingleRequest(requestType);
            parallelParameters.add(estimateParameter);

        });

        QueryEstimateRouteWithBufferTimeParallelRequestType requestType = new QueryEstimateRouteWithBufferTimeParallelRequestType();
        requestType.setParallelParameters(parallelParameters);
        requestType.setTimeout(30000L);

        QueryEstimateRouteWithBufferTimeParallelResponseType queryEstimateRouteWithBufferTimeParallelResponseType = dcsMapDomainServiceProxy.queryEstimateRouteWithBufferTimeParallel(requestType);
        List<EstimateResultDTO> estimateResults = queryEstimateRouteWithBufferTimeParallelResponseType.getEstimateResults();
        if (CollectionUtils.isEmpty(estimateResults)) {
            return  Lists.newArrayList();
        }
        for (EstimateResultDTO estimateResult : estimateResults) {
            logger.info("estimateResult", "estimateResult:{}", estimateResult);
            Integer id = estimateResult.getId();
            QueryEstimateRouteWithBufferTimeResponseType singleResponse = estimateResult.getSingleResponse();
            Integer duration = singleResponse.getDuration();
            Integer distance = singleResponse.getDistance();

        }


        return buildRouteFromNewApi(estimateResults, positions);
    }

    /**
     * 异步监控新接口
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     * @param oldApiRoutes 旧接口结果
     */
    private void asyncMonitorNewApi(Integer cityId, Date useTime, String userOrderId,
                                  List<Position> positions, List<Route> oldApiRoutes) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                List<Route> newApiRoutes = queryRoutesWithNewApi(cityId, useTime, userOrderId, positions);
                compareAndLogRoutes(cityId, useTime, positions, oldApiRoutes, newApiRoutes, false);
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.asyncMonitorNewApi error", e);
            }
        }, driverThreadPool);
    }

    /**
     * 发送延迟路径预估消息
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param userOrderId 用户订单ID
     * @param positions 位置列表
     */
    private void sendDelayedRouteEstimateMessage(Integer cityId, Date useTime, String userOrderId, List<Position> positions) {
        try {
            long delay = useTime.getTime() - System.currentTimeMillis();
            if (delay > 0) {
                Map<String, Object> messageData = new HashMap<>();
                messageData.put("cityId", cityId);
                messageData.put("useTime", useTime);
                messageData.put("userOrderId", userOrderId);
                messageData.put("positions", positions);
                messageData.put("callTime", new Date());

                messageProducer.sendDelayMessage("delayed_route_estimate", messageData, delay);
                logger.info("GeoGatewayImpl.sendDelayedRouteEstimateMessage", "sent delayed message, delay: {}", delay);
            }
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.sendDelayedRouteEstimateMessage error", e);
        }
    }

    /**
     * 比较并记录路径结果
     *
     * @param cityId 城市ID
     * @param useTime 用车时间
     * @param positions 位置列表
     * @param oldApiRoutes 旧接口结果
     * @param newApiRoutes 新接口结果
     * @param isDelayedCall 是否为延迟调用
     */
    private void compareAndLogRoutes(Integer cityId, Date useTime, List<Position> positions,
                                   List<Route> oldApiRoutes, List<Route> newApiRoutes, boolean isDelayedCall) {
        if (!routeApiSwitchConfig.shouldEnableMonitor()) {
            return;
        }

        try {
            Date callTime = new Date();
            for (int i = 0; i < Math.min(positions.size(), Math.min(oldApiRoutes.size(), newApiRoutes.size())); i++) {
                Position position = positions.get(i);
                Route oldRoute = oldApiRoutes.get(i);
                Route newRoute = newApiRoutes.get(i);

                String startPoint = RouteMonitorUtil.extractStartPoint(position);
                String endPoint = RouteMonitorUtil.extractEndPoint(position);
                Double realTimeDuration = oldRoute.getDuration();
                Double futureDuration = newRoute.getDuration();
                Double diff = Math.abs(realTimeDuration - futureDuration);

                if (isDelayedCall) {
                    RouteMonitorUtil.logDelayedRouteEstimate(cityId, useTime, callTime,
                            startPoint, endPoint, futureDuration, realTimeDuration, diff, false);
                } else {
                    RouteMonitorUtil.logRouteComparison(cityId, useTime, callTime,
                            startPoint, endPoint, realTimeDuration, futureDuration, diff, false);
                }
            }
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.compareAndLogRoutes error", e);
        }
    }

    /**
     * 从新接口结果构建Route列表
     *
     * @param estimateResults 预估结果
     * @param positions 位置列表
     * @return Route列表
     */
    private List<Route> buildRouteFromNewApi(List<EstimateResultDTO> estimateResults, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(estimateResults)) {
            return routes;
        }

        for (EstimateResultDTO estimateResult : estimateResults) {
            try {
                Integer id = estimateResult.getId();
                QueryEstimateRouteWithBufferTimeResponseType singleResponse = estimateResult.getSingleResponse();
                if (singleResponse != null && id != null && id < positions.size()) {
                    Position position = positions.get(id);
                    Integer duration = singleResponse.getDuration();
                    Integer distance = singleResponse.getDistance();

                    Route route = new Route(position.hash(),
                            distance != null ? distance.doubleValue() / 1000 : 0.0,
                            duration != null ? duration.doubleValue() / 60 : 0.0);
                    routes.add(route);
                }
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.buildRouteFromNewApi error", e);
            }
        }
        return routes;
    }


    @Override
    public Route queryRoute(Long taskId, Position position) {
        return queryRoute(taskId, position, null);
    }

    /**
     * 查询单个路径（支持用车时间维度）
     *
     * @param taskId 任务ID
     * @param position 位置
     * @param useTime 用车时间
     * @return 路径
     */
    public Route queryRoute(Long taskId, Position position, Date useTime) {
        try {
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                return new Route(position.hash(), 0, 0);
            }

            // 优先使用带时间维度的缓存key
            String key = useTime != null ?
                    Route.toKey(taskId, position.hash(), useTime) :
                    Route.toKey(taskId, position.hash());

            String v = caffeineCache.get(key, k -> (trocksProviderProxy.get(k)));
            boolean cacheHit = StringUtils.isNotBlank(v);

            // 记录缓存命中监控
            if (routeApiSwitchConfig.shouldEnableMonitor()) {
                RouteMonitorUtil.logCacheHit(null, key, cacheHit, "query");
            }

            if (StringUtils.isBlank(v)) {
                MetricsUtil.recordValue("query.route.cache.null", 1);
                return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
            }
            List<String> list = Splitter.on(CommonConstant.PLACEHOLDER).splitToList(v);
            return new Route(position.hash(), Double.valueOf(list.get(0)), Double.valueOf(list.get(1)));
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoute", e);
        }
        MetricsUtil.recordValue("query.route.cache.null", 1);
        return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
    }

    private QueryDistanceBatchRequestType buildQueryDistanceBatchRequestType(Integer cityId, List<Position> list) {
        List<BaseLatLngPairDTO> dtos = Lists.newArrayList();
        for (Position position : list) {
            BaseLatLngPairDTO dto = new BaseLatLngPairDTO();
            dto.setCid(cityId.longValue());
            dto.setCoordType(position.getFromCoordsys());
            dto.setOriginLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            dto.setOriginLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            dto.setDestinationLongitude(BigDecimal.valueOf(position.getToLongitude()));
            dto.setDestinationLatitude(BigDecimal.valueOf(position.getToLatitude()));
            dtos.add(dto);
        }
        QueryDistanceBatchRequestType request = new QueryDistanceBatchRequestType();
        request.setGpsPair(dtos);
        return request;
    }

    private List<Route> buildRoute(QueryDistanceBatchResponseType response) {
        List<Route> list = Lists.newArrayList();
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResults())) {
            return list;
        }
        for (GaodeDistanceInfoDTO dto : response.getResults()) {
            String fromHash = GeoHashUtil.buildGeoHash(dto.getOriginLongitude().doubleValue(), dto.getOriginLatitude().doubleValue());
            String toHash = GeoHashUtil.buildGeoHash(dto.getDestinationLongitude().doubleValue(), dto.getDestinationLatitude().doubleValue());
            Route route = new Route(Position.hash(fromHash, toHash), (double) dto.getDistance() / 1000, (double) dto.getDuration() / 60);
            list.add(route);
        }
        return list;
    }
}
