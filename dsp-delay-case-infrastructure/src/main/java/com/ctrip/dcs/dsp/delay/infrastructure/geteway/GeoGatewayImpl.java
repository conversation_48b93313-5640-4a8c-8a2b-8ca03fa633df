package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelRequestType;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelResponseType;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.location.application.service.interfaces.dto.EstimateParameter;
import com.ctrip.dcs.location.application.service.interfaces.dto.EstimateResultDTO;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.geo.interfaces.dto.BaseLatLngPairDTO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.ctrip.model.QueryEstimateRouteWithBufferTimeRequestType;
import com.ctrip.model.QueryEstimateRouteWithBufferTimeResponseType;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {

    private static final Logger logger = LoggerFactory.getLogger(GeoGatewayImpl.class);

    @Autowired
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Autowired
    private TRocksProviderProxy trocksProviderProxy;

    @Autowired
    private Cache<String, String> caffeineCache;

    @Autowired
    private LbsRateLimiter rateLimiter;

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        List<List<Position>> partition = Lists.partition(positions, 99);
        for (List<Position> list : partition) {
            try {
                double acquire = rateLimiter.acquire();
                logger.info("GeoGatewayImpl.queryRoutes", "get token.time:{}", acquire);
                QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
                QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                routes.addAll(buildRoute(response));
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutes", e);
            }
        }
        return routes;
    }

    public List<Route> queryRoutesNew(Integer cityId, Date useTime, String userOrderId, List<Position> positions) {

        if (CollectionUtils.isEmpty(positions)) {
            return Lists.newArrayList();
        }

        List<EstimateParameter> parallelParameters = Lists.newArrayList();

        AtomicInteger index = new AtomicInteger();
        positions.forEach(position -> {

            QueryEstimateRouteWithBufferTimeRequestType requestType = new QueryEstimateRouteWithBufferTimeRequestType();
            BaseGpsDTO from = new BaseGpsDTO();
            from.setLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            from.setLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            from.setCoordType(position.getFromCoordsys());
            from.setCityId(cityId.longValue());
            requestType.setOrigin(from);
            BaseGpsDTO to = new BaseGpsDTO();
            to.setLatitude(BigDecimal.valueOf(position.getToLatitude()));
            to.setLongitude(BigDecimal.valueOf(position.getToLongitude()));
            to.setCoordType(position.getToCoordsys());
            to.setCityId(cityId.longValue());
            requestType.setDestination(to);
            requestType.setDepartureTime(useTime.getTime());
            requestType.setOrderId(userOrderId);

            ExtendInfoDTO extendInfoDTO = new ExtendInfoDTO();
            extendInfoDTO.setForceGaodeFuture(true);
            requestType.setExtendInfoDTO(extendInfoDTO);

            EstimateParameter estimateParameter = new EstimateParameter();
            estimateParameter.setId(index.getAndIncrement());
            estimateParameter.setSingleRequest(requestType);
            parallelParameters.add(estimateParameter);

        });

        QueryEstimateRouteWithBufferTimeParallelRequestType requestType = new QueryEstimateRouteWithBufferTimeParallelRequestType();
        requestType.setParallelParameters(parallelParameters);
        requestType.setTimeout(30000L);

        QueryEstimateRouteWithBufferTimeParallelResponseType queryEstimateRouteWithBufferTimeParallelResponseType = dcsMapDomainServiceProxy.queryEstimateRouteWithBufferTimeParallel(requestType);
        List<EstimateResultDTO> estimateResults = queryEstimateRouteWithBufferTimeParallelResponseType.getEstimateResults();
        if (CollectionUtils.isEmpty(estimateResults)) {
            return  Lists.newArrayList();
        }
        for (EstimateResultDTO estimateResult : estimateResults) {
            logger.info("estimateResult", "estimateResult:{}", estimateResult);
            Integer id = estimateResult.getId();
            QueryEstimateRouteWithBufferTimeResponseType singleResponse = estimateResult.getSingleResponse();
            Integer duration = singleResponse.getDuration();
            Integer distance = singleResponse.getDistance();

        }


        return null;
    }
    
    
    @Override
    public Route queryRoute(Long taskId, Position position) {
        try {
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                return new Route(position.hash(), 0, 0);
            }
            String key = Route.toKey(taskId, position.hash());
            String v =  caffeineCache.get(key, k -> (trocksProviderProxy.get(k)));
            if (StringUtils.isBlank(v)) {
                MetricsUtil.recordValue("query.route.cache.null", 1);
                return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
            }
            List<String> list = Splitter.on(CommonConstant.PLACEHOLDER).splitToList(v);
            return new Route(position.hash(), Double.valueOf(list.get(0)), Double.valueOf(list.get(1)));
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoute", e);
        }
        MetricsUtil.recordValue("query.route.cache.null", 1);
        return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
    }

    private QueryDistanceBatchRequestType buildQueryDistanceBatchRequestType(Integer cityId, List<Position> list) {
        List<BaseLatLngPairDTO> dtos = Lists.newArrayList();
        for (Position position : list) {
            BaseLatLngPairDTO dto = new BaseLatLngPairDTO();
            dto.setCid(cityId.longValue());
            dto.setCoordType(position.getFromCoordsys());
            dto.setOriginLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            dto.setOriginLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            dto.setDestinationLongitude(BigDecimal.valueOf(position.getToLongitude()));
            dto.setDestinationLatitude(BigDecimal.valueOf(position.getToLatitude()));
            dtos.add(dto);
        }
        QueryDistanceBatchRequestType request = new QueryDistanceBatchRequestType();
        request.setGpsPair(dtos);
        return request;
    }

    private List<Route> buildRoute(QueryDistanceBatchResponseType response) {
        List<Route> list = Lists.newArrayList();
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResults())) {
            return list;
        }
        for (GaodeDistanceInfoDTO dto : response.getResults()) {
            String fromHash = GeoHashUtil.buildGeoHash(dto.getOriginLongitude().doubleValue(), dto.getOriginLatitude().doubleValue());
            String toHash = GeoHashUtil.buildGeoHash(dto.getDestinationLongitude().doubleValue(), dto.getDestinationLatitude().doubleValue());
            Route route = new Route(Position.hash(fromHash, toHash), (double) dto.getDistance() / 1000, (double) dto.getDuration() / 60);
            list.add(route);
        }
        return list;
    }
}
