package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.basic.map.application.service.interfaces.DcsMapDomainServiceClient;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelRequestType;
import com.ctrip.dcs.basic.map.application.service.interfaces.QueryEstimateRouteWithBufferTimeParallelResponseType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteRequestType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(value = DcsMapDomainServiceClient.class, format = "json")
public interface DcsMapDomainServiceProxy {

    QueryPredictRouteResponseType queryEstimateRoute(QueryPredictRouteRequestType request);

    QueryEstimateRouteWithBufferTimeParallelResponseType queryEstimateRouteWithBufferTimeParallel(QueryEstimateRouteWithBufferTimeParallelRequestType request);

}
