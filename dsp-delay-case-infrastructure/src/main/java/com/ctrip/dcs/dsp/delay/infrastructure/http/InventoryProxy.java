package com.ctrip.dcs.dsp.delay.infrastructure.http;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenResultDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class InventoryProxy {

    @Autowired
    private HttpUrlQConfig httpUrlQConfig;



    public void unfrozen(String orderId, String driverId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderId", orderId);
        params.put("driverId", driverId);
        HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                .withUrl(httpUrlQConfig.getUnfrozenUrl())
                .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                .withParam(params)
                .withTypeReference(new TypeReference<QunarResResult>() {})
                .build();
        QunarResResult result = HttpUtil.post(httpPostParam);
        if (result == null || result.getBstatus() == null || result.getBstatus().getCode() != 0) {
            throw new BizException("unfrozen error.orderId:" + orderId);
        }
    }

    public List<FrozenResultDTO> batchFrozen(List<FrozenDTO> list) {
        List<String> orderIds = list.stream().map(FrozenDTO::getOrderId).collect(Collectors.toList());
        List<String> driverIds = list.stream().map(FrozenDTO::getDriverId).collect(Collectors.toList());
        Map<String, Object> params = Maps.newHashMap();
        params.put("orderIds", Joiner.on(",").join(orderIds));
        params.put("driverIds", Joiner.on(",").join(driverIds));
        params.put("autoUnfrozen", YesOrNo.NO.getCode());
        HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                .withUrl(httpUrlQConfig.getBatchFrozenUrl())
                .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                .withParam(params)
                .withTypeReference(new TypeReference<QunarResResult<List<FrozenResultDTO>>>() {})
                .build();
        QunarResResult<List<FrozenResultDTO>> result = HttpUtil.post(httpPostParam);
        return Optional.ofNullable(result).map(QunarResResult::getData).orElse(Lists.newArrayList());
    }
}
