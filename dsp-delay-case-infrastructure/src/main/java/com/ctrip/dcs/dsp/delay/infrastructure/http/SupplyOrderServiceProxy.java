package com.ctrip.dcs.dsp.delay.infrastructure.http;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.RedispatchResultDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.QunarResResult;
import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import com.ctrip.dcs.dsp.delay.infrastructure.util.TokenUtil;
import com.ctrip.dcs.dsp.delay.model.OrderTakenResult;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.UserChoicePackageService;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.HttpUrlQConfig;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.time.DateUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SupplyOrderServiceProxy {

    private static final Logger logger = LoggerFactory.getLogger(SupplyOrderServiceProxy.class);

    @Autowired
    private HttpUrlQConfig httpUrlQConfig;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    public SupplyOrder query(String orderId) {
        try {
            // 参数追加token
            String token = TokenUtil.generateToken(orderId);
            String finalURL = httpUrlQConfig.getOrderInfoDetailUrl() + "?token=" + token;
            logger.info("QBaseDetail_" + orderId, "URL=" + finalURL);

            Map<String, String> params = Maps.newHashMap();
            params.put("orderId", orderId);
            HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                    .withUrl(finalURL)
                    .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                    .withParam(params)
                    .withTypeReference(new TypeReference<QunarResResult<SupplyOrder>>() {})
                    .build();
            QunarResResult<SupplyOrder> result = HttpUtil.post(httpPostParam);
            if (result == null || !result.success()) {
                throw new BizException("query supply order error!");
            }
            return result.getData();
        } catch (Exception e) {
            MetricsUtil.recordValue("query.supply.order.http.error", 1);
            logger.error(e);
        }
        return null;
    }

    public OrderTakenResult taken(String orderId, String driverId, String duid, Integer transportGroupId) {
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("driverId", driverId);
            params.put("orderId", orderId);
            params.put("transportGroupId", transportGroupId);
            params.put("duid", duid);
            params.put("frozen", YesOrNo.NO.getCode());
            HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                    .withUrl(httpUrlQConfig.getOrderTakenUrl())
                    .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                    .withParam(params)
                    .withTypeReference(new TypeReference<QunarResResult<OrderTakenResult>>() {})
                    .build();
            QunarResResult<OrderTakenResult> result = HttpUtil.post(httpPostParam);
            if (result == null || !result.success()) {
                throw new BizException("taken supply order error!");
            }
            return result.getData();
        } catch (BizException e) {
            MetricsUtil.recordValue("taken.supply.order.http.error", 1);
            logger.error(e);
        }
        return null;
    }

    public RedispatchResultDTO redispatch(String orderId) {
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("roleId", delayDspCommonQConfig.getRedispatchRoleId());
            params.put("orderId", orderId);
            params.put("reasonDetailId", delayDspCommonQConfig.getRedispatchReasonDetailId());
            params.put("userName", delayDspCommonQConfig.getRedispatchUserName());
            params.put("chgType", delayDspCommonQConfig.getRedispatchChgType());
            params.put("chgReason", delayDspCommonQConfig.getRedispatchChgReason());
            HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                    .withUrl(httpUrlQConfig.getRedispatchUrl())
                    .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                    .withParam(params)
                    .withTypeReference(new TypeReference<QunarResResult<RedispatchResultDTO>>() {})
                    .build();
            QunarResResult<RedispatchResultDTO> result = HttpUtil.post(httpPostParam);
            if (result == null || !result.success()) {
                throw new BizException("redispatch supply order error!");
            }
            return result.getData();
        } catch (Exception e) {
            MetricsUtil.recordValue("redispatch.supply.order.http.error", 1);
            logger.error(e);
        }
        return null;
    }

    public List<SupplyOrder> queryByDriver(List<String> driverIds) {
        // 查询未来6个月的订单
        Date start = DateUtils.addDays(new Date(), -1);
        Date mid = DateUtils.addMonths(start, 3);
        Date end = DateUtils.addMonths(mid, 3);
        return queryByDriver(driverIds, start, mid, mid, end);
    }

    public List<SupplyOrder> queryByDriver(List<String> driverIds, Date forStartBookTime, Date forEndBookTime, Date backStartBookTime, Date backEndBookTime) {
        String drivIdList = Joiner.on(",").join(driverIds);
        Map<String, Object> params = Maps.newHashMap();
        params.put("drivIdList", drivIdList);
        params.put("forStartBookTime", DateUtil.formatDate(forStartBookTime, DateUtil.DATE_FMT));
        params.put("forEndBookTime", DateUtil.formatDate(forEndBookTime, DateUtil.DATE_FMT));
        params.put("backStartBookTime", DateUtil.formatDate(backStartBookTime, DateUtil.DATE_FMT));
        params.put("backEndBookTime", DateUtil.formatDate(backEndBookTime, DateUtil.DATE_FMT));
        params.put("orderStatus", "3,4,5,6,7");
        params.put("type", "1");
        HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                .withUrl(httpUrlQConfig.getRelateOrdersUrl())
                .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                .withParam(params)
                .withTypeReference(new TypeReference<QunarResResult<Map<String/*orderId*/, SupplyOrder>>>() {})
                .build();
        QunarResResult<Map<String/*orderId*/, SupplyOrder>> result = HttpUtil.post(httpPostParam);
        Map<String, SupplyOrder> map = Optional.ofNullable(result).map(QunarResResult::getData).orElse(Maps.newHashMap());
        return Lists.newArrayList(map.values());
    }


    public List<UserChoicePackageService> queryUserChoicePackageService(String mainOrderId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("userOrderId", mainOrderId);
        HttpUtil.HttpPostParam httpPostParam = new HttpUtil.HttpPostParam.Builder()
                .withUrl(httpUrlQConfig.getUserChoicePackageServiceUrl())
                .withMediaType(HttpUtil.FORM_MEDIA_TYPE)
                .withParam(params)
                .withTypeReference(new TypeReference<QunarResResult<List<UserChoicePackageService>>>() {})
                .build();
        QunarResResult<List<UserChoicePackageService>> result = HttpUtil.post(httpPostParam);
        if (result == null || !result.success()) {
            MetricsUtil.recordValue("query.user.choice.package.http.error", 1);
            throw new BizException("query user choice package service error!");
        }
        return Optional.of(result).map(QunarResResult::getData).orElse(Lists.newArrayList());
    }
}
