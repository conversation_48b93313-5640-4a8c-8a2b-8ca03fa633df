package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

        import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
        import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway;
        import com.ctrip.dcs.dsp.delay.infrastructure.http.InventoryProxy;
        import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenDTO;
        import com.ctrip.dcs.dsp.delay.infrastructure.http.dto.FrozenResultDTO;
        import com.ctrip.dcs.dsp.delay.infrastructure.soa.TransportInventoryServiceProxy;
        import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
        import com.ctrip.dcs.dsp.delay.model.Driver;
        import com.ctrip.dcs.dsp.delay.model.Frozen;
        import com.ctrip.dcs.dsp.delay.model.OccupancyInventoryDTO;
        import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
        import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
        import com.ctrip.dcs.dsp.delay.util.DateUtil;
        import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
        import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryRequestType;
        import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryResponseType;
        import com.ctrip.igt.ResponseResult;
        import com.ctrip.igt.framework.common.clogging.Logger;
        import com.ctrip.igt.framework.common.clogging.LoggerFactory;
        import com.ctrip.platform.dal.dao.helper.JsonUtils;
        import com.google.common.collect.Lists;
        import org.apache.commons.collections.CollectionUtils;
        import org.apache.commons.lang3.StringUtils;
        import org.springframework.beans.factory.annotation.Autowired;
        import org.springframework.stereotype.Component;

        import java.util.ArrayList;
        import java.util.List;
        import java.util.Map;
        import java.util.Objects;
        import java.util.Optional;
        import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class InventoryGatewayImpl implements InventoryGateway {

    private static final Logger logger = LoggerFactory.getLogger(InventoryGatewayImpl.class);

    @Autowired
    private InventoryProxy inventoryProxy;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private TransportInventoryServiceProxy transportInventoryServiceProxy;

    @Override
    public void unfrozen(DelayDspOrder order, Driver driver) {
        if (Objects.isNull(order) || Objects.isNull(driver)) {
            return;
        }
        unfrozen(order.getOrderId(), driver.getDriverId());
    }

    @Override
    public void unfrozen(String orderId, String driverId) {
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(driverId)) {
            return;
        }
        try {
            inventoryProxy.unfrozen(orderId, driverId);
            MetricsUtil.recordValue("inventory.unfrozen", 1);
        } catch (Exception e) {
            logger.error("unfrozen error!", e);
            MetricsUtil.recordValue("inventory.unfrozen.error", 1);
            try {
                inventoryProxy.unfrozen(orderId, driverId);
            } catch (Exception ex) {
                logger.error("unfrozen error!", ex);
            }
        }
    }

    @Override
    public List<Frozen> batchFrozen(List<Frozen> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<FrozenResultDTO> result = Lists.newArrayList();
        try {
            List<FrozenDTO> dtoList = list.stream().map(f -> new FrozenDTO(f.getOrder().getOrderId(), f.getDriver().getDriverId())).collect(Collectors.toList());
            // 限制单次请求参数数量
            List<List<FrozenDTO>> partition = Lists.partition(dtoList, delayDspCommonQConfig.getBatchFrozenSize());
            for (List<FrozenDTO> subList : partition) {
                try {
                    // 批量冻结
                    result.addAll(inventoryProxy.batchFrozen(subList));
                    MetricsUtil.recordValue("inventory.batch.frozen", 1);
                } catch (Exception e) {
                    logger.error("inventory.batch.frozen.error");
                    MetricsUtil.recordValue("inventory.batch.frozen.error", 1);
                }
            }
            Map<String, Frozen> map = list.stream().collect(Collectors.toMap(f -> f.getOrder().getOrderId(), f -> f));
            for (FrozenResultDTO dto : result) {
                Frozen frozen = map.get(dto.getOrderId());
                if (Objects.isNull(frozen)) {
                    continue;
                }
                frozen.setFrozen(dto.getFrozen());
            }
        } catch (Exception e) {
            logger.error("inventory.batch.frozen.error");
            MetricsUtil.recordValue("inventory.batch.frozen.error", 1);
        }
        return list;
    }

    @Override
    public String checkInventoryConflict(List<String> driverIdList, SupplyOrder order) {
        int executeTimes = 0;
        UseVirtualInventoryRequestType useVirtualInventoryRequestType = new UseVirtualInventoryRequestType();
        useVirtualInventoryRequestType.setStartTime(DateUtil.formatDate(order.getSysExpectBookTime(), DateUtil.DATE_FMT));
        useVirtualInventoryRequestType.setEndTime(DateUtil.formatDate(order.getPredicServiceStopTime(), DateUtil.DATE_FMT));
        useVirtualInventoryRequestType.setDspOrderId(order.getOrderId());
        List<List<String>> listMsgPartition = Lists.partition(driverIdList, 100);
        for (List<String> driverIds : listMsgPartition) {
            try {
                executeTimes = executeTimes + 1;
                useVirtualInventoryRequestType.setDriverIds(driverIds);
                UseVirtualInventoryResponseType response = transportInventoryServiceProxy.useVirtualInventory(useVirtualInventoryRequestType);
                Boolean aBoolean = Optional.ofNullable(response).map(UseVirtualInventoryResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
                if (aBoolean && StringUtils.isNotBlank(response.getDriverId())) {
                    MetricsUtil.gaugeValue("insert_pool_checkInventoryConflict_execute_count", executeTimes);
                    return response.getDriverId();
                }
            } catch (Exception e) {
                logger.error("InventoryGatewayImpl_checkInventoryConflict", e);
            }
        }
        MetricsUtil.gaugeValue("insert_pool_checkInventoryConflict_execute_count", executeTimes);
        return null;
    }
}
