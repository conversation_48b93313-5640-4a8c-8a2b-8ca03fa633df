# 路径接口智能切换实现方案

## 概述

本方案实现了从旧的`querydistancebatch`方法到新的`queryEstimateRouteWithBufferTimeParallel`方法的智能切换，支持基于城市、高峰期、开关配置的灵活切换策略，并提供完整的监控埋点和延迟消息处理机制。

## 核心功能

### 1. 智能接口切换
- **Top20城市判断**：通过qconfig配置top20城市列表，只有top20城市才会考虑使用新接口
- **高峰期判断**：支持全局和城市特定的高峰期时间配置，只有在高峰期才使用新接口
- **开关控制**：支持直接切换模式和异步监控模式

### 2. 缓存优化
- **时间维度缓存**：在原有缓存key基础上增加用车时间的小时维度
- **缓存命中监控**：记录缓存命中率和使用情况

### 3. 异步监控
- **对比监控**：异步调用新接口与旧接口结果进行对比
- **性能监控**：记录接口调用时间和成功率

### 4. 延迟消息处理
- **真实时间调用**：在用车时间发送延迟消息，获取真实的路径预估结果
- **结果对比**：将真实结果与之前的预估结果进行对比分析

### 5. 监控埋点
支持10个维度的监控指标：
1. 城市
2. 出发时间
3. 调用时间
4. 起点
5. 终点
6. 实时时长
7. 未来时长
8. 实时调用与未来diff的时长
9. 延迟消息中的实际调用结果与之前的未来diff时长
10. 使用结果是否命中缓存

## 架构设计

### 核心类结构

```
dsp-delay-case-domain/
├── qconfig/
│   ├── Top20CityConfig.java           # Top20城市配置
│   ├── PeakHourConfig.java            # 高峰期配置
│   └── RouteApiSwitchConfig.java      # 接口切换开关配置
├── service/
│   └── RouteComparisonService.java    # 路径比较服务
├── util/
│   └── RouteMonitorUtil.java          # 监控埋点工具
└── model/
    └── Route.java                     # 路径模型（已修改）

dsp-delay-case-infrastructure/
└── geteway/
    └── GeoGatewayImpl.java            # 地理网关实现（已重构）

dsp-delay-case-interface/
└── consumer/
    └── DelayedRouteEstimateConsumer.java  # 延迟消息消费者
```

### 配置文件

#### 1. top20_city.properties
```properties
# Top20城市列表，逗号分隔
top20Cities=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
```

#### 2. peak_hour.properties
```properties
# 全局高峰期配置
globalPeakHours=07:00-09:00,17:00-19:00

# 城市特定高峰期配置
cityPeakHours=1:07:30-09:30,17:30-19:30;2:08:00-10:00,18:00-20:00
```

#### 3. route_api_switch.properties
```properties
# 是否启用新接口
enableNewApi=true
# 是否直接切换
directSwitch=false
# 是否启用异步监控
enableAsyncMonitor=true
# 是否启用延迟消息
enableDelayMessage=true
# 是否启用监控埋点
enableMonitor=true
```

## 使用方式

### 1. 基本调用
```java
// 原有调用方式（保持兼容）
List<Route> routes = geoGateway.queryRoutes(cityId, positions);

// 新的调用方式（支持用车时间）
List<Route> routes = geoGateway.queryRoutes(cityId, positions, useTime, userOrderId);
```

### 2. 缓存使用
```java
// 原有缓存key
String oldKey = Route.toKey(taskId, hash);

// 带时间维度的缓存key
String newKey = Route.toKey(taskId, hash, useTime);
```

### 3. 监控埋点
```java
// 路径比较监控
RouteMonitorUtil.logRouteComparison(cityId, departureTime, callTime,
    startPoint, endPoint, realTimeDuration, futureDuration, diff, cacheHit);

// 延迟路径预估监控
RouteMonitorUtil.logDelayedRouteEstimate(cityId, departureTime, callTime,
    startPoint, endPoint, actualDuration, previousDuration, diff, cacheHit);
```

## 切换策略

### 决策流程
1. **全局开关检查**：`enableNewApi`是否为true
2. **城市检查**：是否为top20城市
3. **高峰期检查**：当前时间是否在高峰期
4. **切换模式选择**：
   - 直接切换模式：直接使用新接口
   - 异步监控模式：使用旧接口，异步调用新接口进行对比

### 切换逻辑图
```
请求 -> 全局开关检查 -> 城市检查 -> 高峰期检查 -> 切换模式选择
                                                    ├─ 直接切换：使用新接口
                                                    └─ 异步监控：使用旧接口 + 异步新接口对比
```

## 监控指标

### Cat.logTags监控场景
- `route_comparison`：路径比较监控
- `route_cache_hit`：缓存命中监控
- `route_api_switch`：接口切换监控
- `delayed_route_estimate`：延迟路径预估监控

### 监控维度
每个监控场景都包含相应的标签维度，支持多维度分析和告警。

## 部署说明

### 1. 配置部署
将配置文件部署到qconfig系统：
- `top20_city.properties`
- `peak_hour.properties`
- `route_api_switch.properties`

### 2. 代码部署
确保所有新增的类和修改的类都已正确部署。

### 3. 监控配置
配置Cat监控告警规则，监控关键指标。

## 测试验证

### 1. 单元测试
运行`RouteApiSwitchIntegrationTest`进行集成测试。

### 2. 配置验证
- 验证top20城市配置是否正确
- 验证高峰期时间配置是否生效
- 验证开关配置是否按预期工作

### 3. 功能验证
- 验证接口切换逻辑
- 验证异步监控功能
- 验证延迟消息处理
- 验证监控埋点数据

## 注意事项

1. **性能影响**：异步监控模式会增加系统负载，需要监控系统性能
2. **配置变更**：配置变更需要通过qconfig系统，支持动态生效
3. **监控数据**：监控数据量较大，需要合理设置采样率
4. **异常处理**：所有关键路径都有异常处理，确保系统稳定性
5. **向后兼容**：保持原有接口的向后兼容性

## 后续优化

1. **智能切换策略**：基于历史数据优化切换策略
2. **缓存策略优化**：根据命中率优化缓存策略
3. **监控告警**：完善监控告警规则
4. **性能优化**：根据监控数据进行性能优化
