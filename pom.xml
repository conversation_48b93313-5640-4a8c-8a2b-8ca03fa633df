<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ctrip.dcs.dsp</groupId>
    <artifactId>dsp-delay-case</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>dsp-delay-case</name>
    <url>https://www.ctrip.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <standard.version>1.0.2</standard.version>
        <log4j2.version>2.17.1</log4j2.version>
        <org.projectlombok.version>1.18.20</org.projectlombok.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <dsp-delay-case.version>1.0.0</dsp-delay-case.version>
    </properties>

    <!-- framework -->
    <parent>
        <groupId>com.ctrip.dcs.pom</groupId>
        <artifactId>dcs-super-pom</artifactId>
        <version>7.3.0</version>
    </parent>

    <!-- Dependencies -->
    <dependencies>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- spock -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.3-groovy-2.5</version>
            <scope>test</scope>
        </dependency>
        <!--groovy -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <version>2.5.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-test-junit5</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-testng</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!-- Management -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.dcs.dsp</groupId>
                <artifactId>dsp-delay-case-application</artifactId>
                <version>${dsp-delay-case.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dsp</groupId>
                <artifactId>dsp-delay-case-domain</artifactId>
                <version>${dsp-delay-case.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dsp</groupId>
                <artifactId>dsp-delay-case-host</artifactId>
                <version>${dsp-delay-case.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dsp</groupId>
                <artifactId>dsp-delay-case-infrastructure</artifactId>
                <version>${dsp-delay-case.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dsp</groupId>
                <artifactId>dsp-delay-case-interface</artifactId>
                <version>${dsp-delay-case.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.dcs.shopping</groupId>
                <artifactId>shopping-common-service</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.car.och.geoservice.v1</groupId>
                <artifactId>geoservice</artifactId>
                <version>0.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.dcs.jnt.price</groupId>
                <artifactId>price-shopping</artifactId>
                <version>0.0.45</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-api</artifactId>
                <version>1.6.25</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dsp.delay</groupId>
                <artifactId>dsp-delay-case-api</artifactId>
                <version>1.1.10</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.self</groupId>
                <artifactId>self-orderquery-service-api</artifactId>
                <version>1.0.61</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dispatchorder</groupId>
                <artifactId>self-dispatchorder-service</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driverorder</groupId>
                <artifactId>self-driver-order-domain-api</artifactId>
                <version>1.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.framework</groupId>
                <artifactId>redis</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.framework</groupId>
                <artifactId>qconfig</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.self</groupId>
                <artifactId>self-transport-inventory-service-api</artifactId>
                <version>1.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.domain</groupId>
                <artifactId>driver-domain-service</artifactId>
                <version>1.0.58</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.scm</groupId>
                <artifactId>scm-sdk</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.map.domain.service</groupId>
                <artifactId>dcs-map-domain-service-client</artifactId>
                <version>1.0.4-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <includes>
                        <include>**/*Spec.java</include>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>dsp-delay-case-interface</module>
        <module>dsp-delay-case-application</module>
        <module>dsp-delay-case-domain</module>
        <module>dsp-delay-case-infrastructure</module>
        <module>dsp-delay-case-host</module>
  </modules>
</project>
