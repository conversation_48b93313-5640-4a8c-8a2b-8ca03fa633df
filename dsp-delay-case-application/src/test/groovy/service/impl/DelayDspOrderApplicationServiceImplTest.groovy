package service.impl

import com.ctrip.dcs.dsp.application.service.impl.DelayDspOrderApplicationServiceImpl
import com.ctrip.dcs.dsp.delay.cache.DistributedCache
import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 11:50
 */
class DelayDspOrderApplicationServiceImplTest extends Specification {
    def testObj = new DelayDspOrderApplicationServiceImpl()
    def dispatchConfigService = Mock(DispatchConfigService)
    def delayDspOrderRepository = Mock(DelayDspOrderRepository)
    def delayDspTaskRepository = Mock(DelayDspTaskRepository)
    def supplyOrderGateway = Mock(SupplyOrderGateway)
    def messageProducer = Mock(MessageProducer)
    def distributedCache = Mock(DistributedCache)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)
    def validatorChain = Mock(ValidatorChain)
    def delayTaskService = Mock(DelayTaskService)
    def driverGateway = Mock(DriverGateway)
    def selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)
    def delayDspOrderService = Mock(DelayDspOrderService)
    def specialDateConfig = Mock(SpecialDateConfig)

    def setup() {
        testObj.delayTaskService = delayTaskService
        testObj.delayDspTaskRepository = delayDspTaskRepository
        testObj.delayDspOrderService = delayDspOrderService
        testObj.delayDspOrderRepository = delayDspOrderRepository
        testObj.supplyOrderGateway = supplyOrderGateway
        testObj.messageProducer = messageProducer
        testObj.validatorChain = validatorChain
        testObj.delayDspCommonQConfig = delayDspCommonQConfig
        testObj.driverGateway = driverGateway
        testObj.selfDispatcherOrderGateway = selfDispatcherOrderGateway
        testObj.distributedCache = distributedCache
        testObj.specialDateConfig = specialDateConfig
        testObj.dispatchConfigService = dispatchConfigService
    }

    @Unroll
    def "test checkSpecialDate"() {
        given:
        delayDspOrderService.checkSpecialDate(_,_,_) >> specialDateValueResult

        when:
        def result = testObj.checkSpecialDate(11, 124, "2024-08-20 11:00:00");

        then:
        Objects.equals(result, expectedResult);

        where:
        specialDateValueResult || expectedResult
        Boolean.FALSE          || Boolean.FALSE
        Boolean.TRUE           || Boolean.TRUE
    }

    @Unroll
    def "test syncDelayDspOrder"() {
        given:
        String orderId = "111";
        delayDspTaskRepository.query(_, _, _) >> [new DelayDspTask(executeTime: new Date())]
        supplyOrderGateway.query(_) >> new SupplyOrder(sysExpectBookTime: new Date(), actualFromLatitude: 11d,actualFromLongitude: 12d, actualToLatitude: 11d, actualToLongitude: 12d)
        validatorChain.validate(_) >> new ValidatorDTO(code: ValidatorCode.OK)
        delayTaskService.queryOrCreate(_, _) >> new DelayDspTask()
        driverGateway.query(_) >> new Driver(cityId: 0, carTypeId: 0)
        delayDspCommonQConfig.getBeforeExecuteTimeHour() >> 1;

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }


    def "test syncDelayDspOrder1"() {
        given:
        String orderId = "111";
        supplyOrderGateway.query(_) >> null;

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }

    def "test syncDelayDspOrder2"() {
        given:
        String orderId = "111";
        supplyOrderGateway.query(_) >> new SupplyOrder(sysExpectBookTime: new Date(), actualFromLatitude: 11d,actualFromLongitude: 12d, actualToLatitude: 11d, actualToLongitude: 12d);
        validatorChain.validate(_) >> new ValidatorDTO(code: ValidatorCode.ERROR)

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }

    def "test syncDelayDspOrder3"() {
        given:
        String orderId = "111";
        supplyOrderGateway.query(_) >> new SupplyOrder(sysExpectBookTime: new Date(), actualFromLatitude: 11d,actualFromLongitude: 12d, actualToLatitude: 11d, actualToLongitude: 12d);
        validatorChain.validate(_) >> new ValidatorDTO(code: ValidatorCode.OK)
        driverGateway.query(_) >> null

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }

    def "test syncDelayDspOrder4"() {
        given:
        String orderId = "111";
        supplyOrderGateway.query(_) >> new SupplyOrder(sysExpectBookTime: new Date(), actualFromLatitude: 11d,actualFromLongitude: 12d, actualToLatitude: 11d, actualToLongitude: 12d);
        validatorChain.validate(_) >> new ValidatorDTO(code: ValidatorCode.OK)
        driverGateway.query(_) >> new Driver(cityId: 0, carTypeId: 0)
        delayDspTaskRepository.query(_, _, _) >> []
        delayTaskService.queryOrCreate(_, _) >> null

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }

    def "test syncDelayDspOrder5"() {
        given:
        String orderId = "111";
        supplyOrderGateway.query(_) >> new SupplyOrder(sysExpectBookTime: new Date(), actualFromLatitude: 11d,actualFromLongitude: 12d, actualToLatitude: 11d, actualToLongitude: 12d);
        validatorChain.validate(_) >> new ValidatorDTO(code: ValidatorCode.OK)
        driverGateway.query(_) >> new Driver(cityId: 0, carTypeId: 0)
        delayDspTaskRepository.query(_, _, _) >> []
        delayTaskService.queryOrCreate(_, _) >> new DelayDspTask(executeTime: new Date())
        delayDspCommonQConfig.getBeforeExecuteTimeHour() >> 1

        when:
        testObj.syncDelayDspOrder(orderId, "2211")

        then:
        Assert.assertTrue(Objects.equals(orderId,"111"));
    }

    def "test updateTaskInfo"() {
        given:

        when:
        def updateRes = delayTaskService.updateTaskInfo(new DelayDspTask())
        then:
        Assert.assertTrue(Objects.isNull(updateRes));
    }
}
