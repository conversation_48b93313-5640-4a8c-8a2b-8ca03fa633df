package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.application.service.impl.DelayDspOrderApplicationServiceImpl;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.IncreaseVirtualDspTakenFailCountRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class DelayDspOrderApplicationServiceTest {

    @InjectMocks
    private DelayDspOrderApplicationServiceImpl delayDspOrderApplicationService;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private SupplyOrderGateway supplyOrderGateway;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private ValidatorChain validatorChain;

    @Mock
    private DelayTaskService delayTaskService;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DelayDspOrderService delayDspOrderService;

    @Mock
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Test
    public void testInsertDelayDspOrderNull() {
        InsertDelayDspOrderRequestType request = new InsertDelayDspOrderRequestType();
        PowerMockito.when(supplyOrderGateway.query(Mockito.anyString())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        InsertDelayDspOrderResponseType responseType = delayDspOrderApplicationService.insertDelayDspOrder(request);
        Assert.assertEquals((long)responseType.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspOrderValidateNotOK() {
        InsertDelayDspOrderRequestType request = new InsertDelayDspOrderRequestType();
        SupplyOrder order = new SupplyOrder();
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.ERROR);

        PowerMockito.when(validatorChain.validate(Mockito.any())).thenReturn(validatorDTO);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        InsertDelayDspOrderResponseType responseType = delayDspOrderApplicationService.insertDelayDspOrder(request);
        Assert.assertEquals((long)responseType.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspOrderNoTask() {
        InsertDelayDspOrderRequestType request = new InsertDelayDspOrderRequestType();
        SupplyOrder order = new SupplyOrder();
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(Mockito.any())).thenReturn(validatorDTO);
        PowerMockito.when(delayTaskService.queryOrCreate(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        InsertDelayDspOrderResponseType responseType = delayDspOrderApplicationService.insertDelayDspOrder(request);
        Assert.assertEquals((long)responseType.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspOrderAfterBookTime() {
        InsertDelayDspOrderRequestType request = new InsertDelayDspOrderRequestType();
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setActualFromLongitude(121.354984);
        order.setActualFromLatitude(31.199124);
        order.setActualToLongitude(121.354984);
        order.setActualToLatitude(31.199124);
        order.setPredictPriceInfo(new PredictPriceInfo());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setExecuteTime(new Date());
        task.setExecuteTimeDeadline(new Date());
        DelayDspTaskValue taskValue = new DelayDspTaskValue();
        taskValue.setBegin("20:00");
        taskValue.setEnd("07:59");
        taskValue.setHour(10);
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        DelayDspSubSkuValue dspSubSkuValue = new DelayDspSubSkuValue();
        dspSubSkuValue.setInSubSkuId(8888);
        dspSubSkuValue.setOutSubSkuIds("8889,9000");
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.when(delayDspTaskRepository.query(Mockito.any())).thenReturn(null);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(Mockito.any())).thenReturn(validatorDTO);
        PowerMockito.when(dispatchConfigService.matchDelayDspSubSkuConfig(Mockito.any(SupplyOrder.class))).thenReturn(dspSubSkuValue);
        PowerMockito.when(delayTaskService.queryOrCreate(Mockito.any())).thenReturn(task);
        PowerMockito.doNothing().when(delayDspOrderRepository).save(Mockito.any(DelayDspOrder.class));
        PowerMockito.doNothing().when(messageProducer).sendMessage(Mockito.any(), Mockito.any());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        InsertDelayDspOrderResponseType responseType = delayDspOrderApplicationService.insertDelayDspOrder(request);
        Assert.assertEquals(0, (int) responseType.getIsDelay());
    }

    @Test
    public void testInsertDelayDspOrder() {
        InsertDelayDspOrderRequestType request = new InsertDelayDspOrderRequestType();
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setActualFromLongitude(121.354984);
        order.setActualFromLatitude(31.199124);
        order.setActualToLongitude(121.354984);
        order.setActualToLatitude(31.199124);
        order.setPredictPriceInfo(new PredictPriceInfo());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setExecuteTime(DateUtil.parseDate("2022-04-28 10:00:00"));
        task.setExecuteTimeDeadline(new Date());
        DelayDspTaskValue taskValue = new DelayDspTaskValue();
        taskValue.setBegin("20:00");
        taskValue.setEnd("07:59");
        taskValue.setHour(10);
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        DelayDspSubSkuValue dspSubSkuValue = new DelayDspSubSkuValue();
        dspSubSkuValue.setInSubSkuId(8888);
        dspSubSkuValue.setOutSubSkuIds("8889,9000");
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.when(delayDspTaskRepository.query(Mockito.any())).thenReturn(null);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(Mockito.any())).thenReturn(validatorDTO);
        PowerMockito.when(dispatchConfigService.matchDelayDspSubSkuConfig(Mockito.any(SupplyOrder.class))).thenReturn(dspSubSkuValue);
        PowerMockito.when(delayTaskService.queryOrCreate(Mockito.any())).thenReturn(task);
        PowerMockito.doNothing().when(delayDspOrderRepository).save(Mockito.any(DelayDspOrder.class));
        PowerMockito.doNothing().when(messageProducer).sendMessage(Mockito.any(), Mockito.any());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        InsertDelayDspOrderResponseType responseType = delayDspOrderApplicationService.insertDelayDspOrder(request);
        Assert.assertEquals(1, (int) responseType.getIsDelay());
    }

    @Test
    public void testUpdateDelayDspOrderNull() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(null);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrder(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void testUpdateDelayDspOrderNullTask() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(null);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrder(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void testUpdateDelayDspOrderTaskHasExecuted() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrder(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void testUpdateDelayDspOrderNullSupplyOrder() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrder(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void testUpdateDelayDspOrder() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setActualFromLongitude(121.354984);
        order.setActualFromLatitude(31.199124);
        order.setActualToLongitude(121.354984);
        order.setActualToLatitude(31.199124);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.doNothing().when(delayDspOrderRepository).update(Mockito.any());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrder(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void testIncreaseVirtualDspTakenFailCountOff() {
        IncreaseVirtualDspTakenFailCountRequestType request = new IncreaseVirtualDspTakenFailCountRequestType();
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(YesOrNo.NO.getCode());
        Long l = delayDspOrderApplicationService.increaseVirtualDspTakenFailCount(request);
        Assert.assertEquals(NumberUtils.LONG_ZERO, l);
    }

    @Test
    public void testIncreaseVirtualDspTakenFailCountNull() {
        IncreaseVirtualDspTakenFailCountRequestType request = new IncreaseVirtualDspTakenFailCountRequestType();
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        Long l = delayDspOrderApplicationService.increaseVirtualDspTakenFailCount(request);
        Assert.assertEquals(NumberUtils.LONG_ZERO, l);
    }

    @Test
    public void testIncreaseVirtualDspTakenFailCount() {
        IncreaseVirtualDspTakenFailCountRequestType request = new IncreaseVirtualDspTakenFailCountRequestType();
        request.setOrderId("123");
        DelayDspOrder order = new DelayDspOrder();
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setCityId(1);
        order.setCarTypeId(117);
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(order);
        PowerMockito.when(distributedCache.sadd(Mockito.anyString(), Mockito.anyLong(), Mockito.anyString())).thenReturn(1L);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(distributedCache.scard(Mockito.anyString())).thenReturn(1L);
        long l = delayDspOrderApplicationService.increaseVirtualDspTakenFailCount(request);
        Assert.assertEquals(1L, l);
    }

    @Test
    public void testQueryDelayDspOrderNullOrder() {
        QueryDelayDspOrderRequestType request = new QueryDelayDspOrderRequestType();
        request.setOrderId("123");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderResponseType response = delayDspOrderApplicationService.queryDelayDspOrder(request);
        Assert.assertTrue(YesOrNo.NO.getCode() == response.getIsDelay());
    }

    @Test
    public void testQueryDelayDspOrderNullMainOrder() {
        QueryDelayDspOrderRequestType request = new QueryDelayDspOrderRequestType();
        request.setMainOrderId("123");
        PowerMockito.when(delayDspOrderRepository.queryByMainOrderId(Mockito.anyString())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderResponseType response = delayDspOrderApplicationService.queryDelayDspOrder(request);
        Assert.assertTrue(YesOrNo.NO.getCode() == response.getIsDelay());
    }

    @Test
    public void testQueryDelayDspOrderNullTask() {
        QueryDelayDspOrderRequestType request = new QueryDelayDspOrderRequestType();
        request.setOrderId("123");
        DelayDspOrder order = new DelayDspOrder();
        order.setTaskId(1L);
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(order);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderResponseType response = delayDspOrderApplicationService.queryDelayDspOrder(request);
        Assert.assertTrue(YesOrNo.NO.getCode() == response.getIsDelay());
    }

    @Test
    public void testQueryDelayDspOrder() {
        QueryDelayDspOrderRequestType request = new QueryDelayDspOrderRequestType();
        request.setOrderId("123");
        DelayDspOrder order = new DelayDspOrder();
        order.setOrderId("123");
        order.setTaskId(1L);
        order.setIsDelay(YesOrNo.YES.getCode());
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setExecuteTimeDeadline(new Date());
        task.setExecuteTime(new Date());
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(order);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderResponseType response = delayDspOrderApplicationService.queryDelayDspOrder(request);
        Assert.assertTrue(YesOrNo.YES.getCode() == response.getIsDelay());
    }

    @Test
    public void testQueryDelayDspOrderBatchNullParam() {
        QueryDelayDspOrderBatchRequestType request = new QueryDelayDspOrderBatchRequestType();
        request.setOrderIds(Lists.newArrayList());
        PowerMockito.when(delayDspOrderRepository.queryByOrderIds(Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderBatchResponseType response = delayDspOrderApplicationService.queryDelayDspOrderBatch(request);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getData()));
    }

    @Test
    public void testQueryDelayDspOrderBatchNullOrder() {
        QueryDelayDspOrderBatchRequestType request = new QueryDelayDspOrderBatchRequestType();
        request.setOrderIds(Lists.newArrayList("123"));
        PowerMockito.when(delayDspOrderRepository.queryByOrderIds(Mockito.any())).thenReturn(Lists.newArrayList());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderBatchResponseType response = delayDspOrderApplicationService.queryDelayDspOrderBatch(request);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getData()));
    }

    @Test
    public void testQueryDelayDspOrderBatch() {
        QueryDelayDspOrderBatchRequestType request = new QueryDelayDspOrderBatchRequestType();
        request.setOrderIds(Lists.newArrayList("1", "2", "3"));
        DelayDspOrder order1 = new DelayDspOrder();
        order1.setOrderId("1");
        order1.setTaskId(1L);
        order1.setIsDelay(YesOrNo.YES.getCode());
        DelayDspOrder order2 = new DelayDspOrder();
        order2.setOrderId("2");
        order2.setTaskId(1L);
        order2.setIsDelay(YesOrNo.NO.getCode());
        DelayDspOrder order3 = new DelayDspOrder();
        order3.setOrderId("3");
        order3.setTaskId(2L);
        order3.setIsDelay(YesOrNo.YES.getCode());
        DelayDspTask task1 = new DelayDspTask();
        task1.setTaskId(1L);
        task1.setTaskStatus(0);
        task1.setExecuteTime(DateUtil.parseDate("2021-06-27 12:00:00"));
        task1.setExecuteTimeDeadline(DateUtil.parseDate("2021-06-27 13:00:00"));
        DelayDspTask task2 = new DelayDspTask();
        task2.setTaskId(2L);
        task2.setTaskStatus(1);
        task2.setExecuteTime(DateUtil.parseDate("2021-06-27 22:00:00"));
        task2.setExecuteTimeDeadline(DateUtil.parseDate("2021-06-27 23:00:00"));
        PowerMockito.when(delayDspOrderRepository.queryByOrderIds(Mockito.any())).thenReturn(Lists.newArrayList(order1, order2, order3));
        PowerMockito.when(delayDspTaskRepository.queryByTaskIds(Mockito.any())).thenReturn(Lists.newArrayList(task1, task2));
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        QueryDelayDspOrderBatchResponseType response = delayDspOrderApplicationService.queryDelayDspOrderBatch(request);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData()));
    }

    @Test
    public void testSendMessage() {
        DelayDspOrder order = new DelayDspOrder();
        order.setOrderId("1");
        order.setIsDelay(1);
        order.setDuid("duid");
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setExecuteTime(DateUtil.addHours(new Date(), 2));
        PowerMockito.when(delayDspCommonQConfig.getBeforeExecuteTimeHour()).thenReturn(1);
        long delay = delayDspOrderApplicationService.sendMessage(order, task);
        Assert.assertTrue(delay > 0);
        PowerMockito.when(delayDspCommonQConfig.getBeforeExecuteTimeHour()).thenReturn(3);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delay = delayDspOrderApplicationService.sendMessage(order, task);
        Assert.assertTrue(delay == 0);
    }

    @Test
    public void testInsertTakenDelayDspOrder() {
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setActualFromLongitude(121.354984);
        order.setActualFromLatitude(31.199124);
        order.setActualToLongitude(121.354984);
        order.setActualToLatitude(31.199124);
        order.setPredictPriceInfo(new PredictPriceInfo());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setExecuteTime(new Date());
        task.setExecuteTimeDeadline(new Date());
        DelayDspTaskValue taskValue = new DelayDspTaskValue();
        taskValue.setBegin("20:00");
        taskValue.setEnd("07:59");
        taskValue.setHour(10);
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        DelayDspSubSkuValue dspSubSkuValue = new DelayDspSubSkuValue();
        dspSubSkuValue.setInSubSkuId(8888);
        dspSubSkuValue.setOutSubSkuIds("8889,9000");
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.when(selfDispatcherOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.when(delayDspTaskRepository.query(Mockito.any())).thenReturn(null);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(Mockito.any())).thenReturn(validatorDTO);
        PowerMockito.when(dispatchConfigService.matchDelayDspSubSkuConfig(new SupplyOrder())).thenReturn(dspSubSkuValue);
        PowerMockito.when(delayTaskService.queryOrCreate(Mockito.any())).thenReturn(task);
        PowerMockito.doNothing().when(delayDspOrderRepository).save(Mockito.any(DelayDspOrder.class));
        PowerMockito.doNothing().when(messageProducer).sendMessage(Mockito.any(), Mockito.any());
        delayDspOrderApplicationService.insertTakenDelayOrder("1", OrderSource.QUNAR.name());
        order.setOrderStatus(3);
        order.setDriverId("1");
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(new Driver());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.insertTakenDelayOrder("1", OrderSource.CTRIP.name());
        Mockito.verify(messageProducer, Mockito.times(1)).sendMessage(Mockito.any(), Mockito.any());
    }


    @Test
    public void updateDelayDspOrderForCancel1() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(null);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrderForCancel(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void updateDelayDspOrderForCancel2() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(null);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrderForCancel(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void updateDelayDspOrderForCancel3() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrderForCancel(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void updateDelayDspOrderForCancel4() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrderForCancel(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }

    @Test
    public void updateDelayDspOrderForCancel5() {
        UpdateDelayDspOrderRequest request = new UpdateDelayDspOrderRequest("");
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));
        order.setActualFromLongitude(121.354984);
        order.setActualFromLatitude(31.199124);
        order.setActualToLongitude(121.354984);
        order.setActualToLatitude(31.199124);
        PowerMockito.when(supplyOrderGateway.query(Mockito.any())).thenReturn(order);
        PowerMockito.doNothing().when(delayDspOrderRepository).update(Mockito.any());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspOrderApplicationService.updateDelayDspOrderForCancel(request);
        Mockito.verify(delayDspOrderRepository, Mockito.times(0)).update(Mockito.any());
    }
}
