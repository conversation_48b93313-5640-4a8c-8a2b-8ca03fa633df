package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.service.impl.ShortOrderDispatchTaskApplicationServiceImpl;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.limit.TakenRateLimiter;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.*;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class ShortOrderDspTaskApplicationServiceTest {

    @InjectMocks
    private ShortOrderDispatchTaskApplicationServiceImpl shortOrderDispatchTaskApplicationServiceImpl;

    @Mock
    private DispatchService dispatchService;

    @Mock
    private DriverService driverService;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private DispatchTaskService dispatchTaskService;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private TakenRateLimiter limiter;

    @Mock
    private SupplyOrderService supplyOrderService;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DispatcherDriverService dispatcherDriverService;
    @Mock
    private DelayDspContextFactory delayDspContextFactory;

    @Test
    public void testDispatchNullTask() {
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        shortOrderDispatchTaskApplicationServiceImpl.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatchNullOrder() {
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong(), Mockito.anyInt())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        shortOrderDispatchTaskApplicationServiceImpl.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatchNullOrder1() {
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setTaskStatus(DelayDspTaskStatus.EXECUTING.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong(), Mockito.anyInt())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        shortOrderDispatchTaskApplicationServiceImpl.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch() {
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setTaskStatus(DelayDspTaskStatus.EXECUTING.getCode());
        task.setTaskType("SD");
        DelayDspOrder order = new DelayDspOrder();
        DispatcherConfig config = new DispatcherConfig();
        DriverAggregation aggregation = new DriverAggregation();
        aggregation.setDriver(new Driver());
        aggregation.setScore(new DriverScore());
        DispatchResult result = new DispatchResult();
        DispatchResultDetail detail1 = new DispatchResultDetail(order);
        DispatchResultDetail detail2 = new DispatchResultDetail(aggregation, order, 0, new Route());
        result.setDetails(Lists.newArrayList(detail1, detail2));

        DelayDspContext delayDspContext = new DelayDspContext(task, Lists.newArrayList(order), null, null);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong(), Mockito.anyInt())).thenReturn(Lists.newArrayList(order));

        PowerMockito.when(delayDspContextFactory.createSDDelayDspContext(Mockito.any(),Mockito.any())).thenReturn(delayDspContext);
        DispatcherManager.register(DelayDspTaskType.SD, dispatchTaskService);
        PowerMockito.when(dispatchConfigService.buildDispatcherConfig(task)).thenReturn(config);
        PowerMockito.when(driverService.queryDriverAggregation(null, task)).thenReturn(Lists.newArrayList(aggregation));
        PowerMockito.when(limiter.acquire()).thenReturn(0D);
        PowerMockito.when(dispatchTaskService.dispatch(Mockito.any())).thenReturn(result);

//        PowerMockito.doNothing().when(supplyOrderService).batchFrozen(Mockito.any());
        PowerMockito.doNothing().when(delayDspTaskRepository).update(task.getTaskId(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        PowerMockito.doNothing().when(delayDspTaskRepository).save(Mockito.anyList());
        PowerMockito.mockStatic(MetricsFactory.class);
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class);
        shortOrderDispatchTaskApplicationServiceImpl.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(1)).update(Mockito.anyLong(), Mockito.anyInt());
    }


}
