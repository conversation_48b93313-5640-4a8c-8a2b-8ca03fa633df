package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.request.SaveAdjacencyRouteRequest;
import com.ctrip.dcs.dsp.application.service.impl.DelayDspTaskApplicationServiceImpl;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.limit.TakenRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.DriverScore;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.*;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class DelayDspTaskApplicationServiceTest {

    @InjectMocks
    private DelayDspTaskApplicationServiceImpl delayDspTaskApplicationService;

    @Mock
    private DispatchService dispatchService;

    @Mock
    private DriverService driverService;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private GeoGateway geoGateway;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private TakenRateLimiter limiter;

    @Mock
    private SupplyOrderService supplyOrderService;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DispatcherDriverService dispatcherDriverService;

    @Test
    public void testDispatchNullTask() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatchNullOrder() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong(), Mockito.anyInt())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
        request.setTaskId(1L);
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setTaskStatus(DelayDspTaskStatus.EXECUTING.getCode());
        DelayDspOrder order = new DelayDspOrder();
        DispatcherConfig config = new DispatcherConfig();
        DriverAggregation aggregation = new DriverAggregation();
        aggregation.setDriver(new Driver());
        aggregation.setScore(new DriverScore());
        DispatchResult result = new DispatchResult();
        DispatchResultDetail detail1 = new DispatchResultDetail(order);
        DispatchResultDetail detail2 = new DispatchResultDetail(aggregation, order, 0, new Route());
        result.setDetails(Lists.newArrayList(detail1, detail2));
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong(), Mockito.anyInt())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(dispatchConfigService.buildDispatcherConfig(task)).thenReturn(config);
        PowerMockito.when(driverService.queryDriverAggregation(null,task)).thenReturn(Lists.newArrayList(aggregation));
        PowerMockito.when(limiter.acquire()).thenReturn(0D);
        PowerMockito.when(dispatchService.dispatch(Mockito.any())).thenReturn(result);
        PowerMockito.doNothing().when(delayDspTaskRepository).update(task.getTaskId(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        PowerMockito.doNothing().when(delayDspTaskRepository).save(Mockito.anyList());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(1)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testSaveAdjacencyRouteNullTask() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SaveAdjacencyRouteRequest request = new SaveAdjacencyRouteRequest(1L, "1");
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(request);
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }

    @Test
    public void testSaveAdjacencyRouteTaskExecuted() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SaveAdjacencyRouteRequest request = new SaveAdjacencyRouteRequest(1L, "1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.EXECUTING.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(request);
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }

    @Test
    public void testSaveAdjacencyRouteNullOrder() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SaveAdjacencyRouteRequest request = new SaveAdjacencyRouteRequest(1L, "1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(null);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(request);
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }

    @Test
    public void testSaveAdjacencyRouteOrderCancel() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SaveAdjacencyRouteRequest request = new SaveAdjacencyRouteRequest(1L, "1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        DelayDspOrder order = new DelayDspOrder();
        order.setIsCancel(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(order);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(request);
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }

    @Test
    public void testSaveAdjacencyRoute() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        SaveAdjacencyRouteRequest request = new SaveAdjacencyRouteRequest(1L, "1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        DelayDspOrder order = new DelayDspOrder();
        order.setIsCancel(YesOrNo.NO.getCode());
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.anyLong())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByOrderId(Mockito.anyString())).thenReturn(order);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.anyLong())).thenReturn(Lists.newArrayList());
        PowerMockito.when(driverService.queryDriver(Mockito.any(DelayDspTask.class))).thenReturn(Lists.newArrayList());
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(request);
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }

    @Test
    public void testSaveAdjacencyRoute2() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        task.setCityId(1);
        task.setExecuteTime(new Date());
        task.setExecuteTimeDeadline(new Date());
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        task.setEndTime(DateUtil.parseDate("2022-04-29 20:00:00"));
        DelayDspOrder order = new DelayDspOrder();
        order.setOrderId("1");
        order.setIsCancel(YesOrNo.NO.getCode());
        order.setIsDelay(YesOrNo.NO.getCode());
        order.setDriverId("a");
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-29 09:00:00"));
        order.setPredictServiceStopTime(DateUtil.parseDate("2022-04-29 09:30:00"));
        order.setFromLongitude(122.354984);
        order.setFromLatitude(32.199124);
        order.setFromHash(GeoHashUtil.buildGeoHash(122.354984, 32.199124));
        order.setToLongitude(122.354984);
        order.setToLatitude(32.199124);
        order.setToHash(GeoHashUtil.buildGeoHash(122.354984, 32.199124));
        Driver d1 = new Driver();
        d1.setDriverId("a");
        d1.setAddressLongitude(121.354984);
        d1.setAddressLatitude(31.199124);
        Driver d2 = new Driver();
        d2.setDriverId("b");

        DelayDspOrder aj = new DelayDspOrder();
        aj.setOrderId("2");
        aj.setIsCancel(0);
        aj.setIsDelay(1);
        aj.setSysExpectBookTime(DateUtil.parseDate("2022-04-29 08:00:00"));
        aj.setPredictServiceStopTime(DateUtil.parseDate("2022-04-29 08:30:00"));
        aj.setFromLongitude(122.354984);
        aj.setFromLatitude(32.199124);
        aj.setFromHash(GeoHashUtil.buildGeoHash(123.354984, 32.199124));
        aj.setToLongitude(123.354984);
        aj.setToLatitude(31.199124);
        aj.setToHash(GeoHashUtil.buildGeoHash(123.354984, 31.199124));
        Route route = new Route();
        PowerMockito.when(distributedCache.mget(Mockito.anyList())).thenReturn(Lists.newArrayList("null", "nil"));
        PowerMockito.when(geoGateway.queryRoutes(Mockito.anyInt(), Mockito.anyList())).thenReturn(Lists.newArrayList(route));
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        delayDspTaskApplicationService.saveAdjacencyEdge(task, Lists.newArrayList(aj), order, Lists.newArrayList(d1, d2));
        Mockito.verify(distributedCache, Mockito.times(0)).mset(Mockito.anyInt(), Mockito.any());
    }
}
