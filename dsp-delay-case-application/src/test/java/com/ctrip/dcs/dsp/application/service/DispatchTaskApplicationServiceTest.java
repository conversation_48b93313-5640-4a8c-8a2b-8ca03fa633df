package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.service.impl.DispatchTaskApplicationServiceImpl;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.*;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class, JsonUtil.class, DispatcherManager.class})
public class DispatchTaskApplicationServiceTest {

    @InjectMocks
    private DispatchTaskApplicationServiceImpl insertDelayDspPoolApplicationService;

    @Mock
    private DriverService driverService;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private GeoGateway geoGateway;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private SupplyOrderService supplyOrderService;

    @Mock
    private DelayDspContextFactory delayDspContextFactory;

    @Mock
    private SelfDriverOrderGateway driverOrderGateway;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private DispatchDelayDspTaskRequest request;

    @Mock
    private DelayDspTask task;

    @Mock
    private DelayDspOrder order;

    @Mock
    private Driver driver;

    @Mock
    private DelayDspContext delayDspContext;

    @Mock
    private DispatchTaskService dispatchTaskService;

    @Mock
    private DispatchResult dispatchResult;

    @Mock
    private DispatchResultDetail dispatchResultDetail;
    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DispatcherDriverService dispatcherDriverService;
    @Test
    public void testDispatch1() {
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(null);
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch2() {
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(null);
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch3() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch4() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getMatchSuccess()).thenReturn(1);
        PowerMockito.when(order.getIsDelay()).thenReturn(1);
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch5() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getMatchSuccess()).thenReturn(1);
        PowerMockito.when(order.getIsDelay()).thenReturn(1);
        PowerMockito.when(order.getOrderSource()).thenReturn(OrderSource.QUNAR.name());
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testDispatch6() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getMatchSuccess()).thenReturn(1);
        PowerMockito.when(order.getIsDelay()).thenReturn(1);
        PowerMockito.when(order.getOrderSource()).thenReturn(OrderSource.QUNAR.name());
        insertDelayDspPoolApplicationService.dispatch(request);
        Mockito.verify(delayDspTaskRepository, Mockito.times(1)).update(Mockito.anyLong(), Mockito.anyInt());
    }

    @Test
    public void testSaveDispatchResult() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getDriver()).thenReturn(driver);
        PowerMockito.when(dispatchResultDetail.getMatchSuccess()).thenReturn(1);
        PowerMockito.when(order.getIsDelay()).thenReturn(1);
        PowerMockito.when(order.getOrderSource()).thenReturn(OrderSource.QUNAR.name());
        insertDelayDspPoolApplicationService.saveDispatchResult(task, Lists.newArrayList(dispatchResultDetail));
        Mockito.verify(delayDspTaskRepository, Mockito.times(1)).save(Mockito.anyList());
    }

    @Test
    public void testSaveDispatchResult1() {
        PowerMockito.mockStatic(DispatcherManager.class );
        PowerMockito.when(DispatcherManager.get(Mockito.any())).thenReturn(dispatchTaskService);
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(task);
        PowerMockito.when(delayDspOrderRepository.queryByTaskId(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(order));
        PowerMockito.when(delayDspContextFactory.createDelayDspContext(Mockito.any(), Mockito.any())).thenReturn(delayDspContext);
        PowerMockito.when(task.getTaskId()).thenReturn(1L);
        PowerMockito.when(task.getTaskType()).thenReturn(DelayDspTaskType.DP.name());
        PowerMockito.when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode());
        PowerMockito.when(dispatchTaskService.dispatch(delayDspContext)).thenReturn(new DispatchResult());
        PowerMockito.when(dispatchResult.getMatchDetails()).thenReturn(Lists.newArrayList(dispatchResultDetail));
        PowerMockito.when(dispatchResultDetail.getOrder()).thenReturn(order);
        PowerMockito.when(dispatchResultDetail.getDriver()).thenReturn(driver);
        PowerMockito.when(dispatchResultDetail.getMatchSuccess()).thenReturn(1);
        PowerMockito.when(order.getIsDelay()).thenReturn(1);
        PowerMockito.when(order.getOrderSource()).thenReturn(OrderSource.QUNAR.name());
        PowerMockito.when(delayDspTaskRepository.queryRecordByTaskId(1L, 0)).thenReturn(Lists.newArrayList(new DelayDspTaskRecord()));
        insertDelayDspPoolApplicationService.saveDispatchResult(task, Lists.newArrayList(dispatchResultDetail));
        Mockito.verify(delayDspTaskRepository, Mockito.times(0)).save(Mockito.anyList());
    }
}
