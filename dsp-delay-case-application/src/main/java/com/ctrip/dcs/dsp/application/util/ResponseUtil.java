package com.ctrip.dcs.dsp.application.util;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.transport.inventory.service.api.dto.DelayDspOrderDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ResponseUtil {

    public static InsertDelayDspPoolResponseType buildInsertDelayDspPoolResponseType(String orderId) {
        return buildInsertDelayDspPoolResponseType(orderId, YesOrNo.NO.getCode(), new Date(), new Date(), YesOrNo.NO.getCode());
    }

    public static InsertDelayDspPoolResponseType buildInsertDelayDspPoolResponseType(String orderId, Integer isDelay, Date executeTime, Date executeTimeDeadline, int isVirtualDsp) {
        InsertDelayDspPoolResponseType response = new InsertDelayDspPoolResponseType();
        response.setOrderId(orderId);
        response.setIsDelay(isDelay);
        response.setExecuteTime(DateUtil.formatDate(executeTime, DateUtil.DATE_FMT));
        response.setExecuteTimeDeadline(DateUtil.formatDate(executeTimeDeadline, DateUtil.DATE_FMT));
        response.setIsVirtualDsp(isVirtualDsp);
        return response;
    }

    public static InsertDelayDspOrderResponseType buildInsertDelayDspOrderResponse(String orderId) {
        return buildInsertDelayDspOrderResponse(orderId, YesOrNo.NO.getCode(), new Date(), new Date(), YesOrNo.NO.getCode());
    }

    public static InsertDelayDspOrderResponseType buildInsertDelayDspOrderResponse(String orderId, Integer isDelay, Date executeTime, Date executeTimeDeadline, Integer isVirtualDsp) {
        InsertDelayDspOrderResponseType response = new InsertDelayDspOrderResponseType();
        response.setOrderId(orderId);
        response.setIsDelay(isDelay);
        response.setExecuteTime(DateUtil.formatDate(executeTime, DateUtil.DATE_FMT));
        response.setExecuteTimeDeadline(DateUtil.formatDate(executeTimeDeadline, DateUtil.DATE_FMT));
        response.setIsVirtualDsp(isVirtualDsp);
        return response;
    }

    public static QueryDelayDspOrderResponseType buildQueryDelayDspOrderResponse() {
        return buildQueryDelayDspOrderResponse(null, YesOrNo.NO.getCode(), null, new Date(), new Date());
    }

    public static QueryDelayDspOrderResponseType buildQueryDelayDspOrderResponse(String orderId) {
        return buildQueryDelayDspOrderResponse(orderId, YesOrNo.NO.getCode(), null, new Date(), new Date());
    }

    public static QueryDelayDspOrderResponseType buildQueryDelayDspOrderResponse(String orderId, Integer isDelay, Long taskId, Date executeTime, Date executeTimeDeadline) {
        QueryDelayDspOrderResponseType response = new QueryDelayDspOrderResponseType();
        response.setOrderId(orderId);
        response.setIsDelay(isDelay);
        response.setTaskId(taskId);
        response.setExecuteTime(DateUtil.formatDate(executeTime, DateUtil.DATE_FMT));
        response.setExecuteTimeDeadline(DateUtil.formatDate(executeTimeDeadline, DateUtil.DATE_FMT));
        return response;
    }

    public static QueryDelayDspOrderBatchResponseType buildQueryDelayDspOrderBatchResponse() {
        QueryDelayDspOrderBatchResponseType response = new QueryDelayDspOrderBatchResponseType();
        response.setData(Lists.newArrayList());
        return response;
    }

    public static QueryDelayDspOrderBatchResponseType buildQueryDelayDspOrderBatchResponse(List<DelayDspOrder> list, List<DelayDspTask> tasks) {
        QueryDelayDspOrderBatchResponseType response = new QueryDelayDspOrderBatchResponseType();
        response.setData(Lists.newArrayList());
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(tasks)) {
            return response;
        }
        Map<Long /*taskId*/, DelayDspTask> taskMap = tasks.stream().collect(Collectors.toMap(DelayDspTask::getTaskId, t -> t));
        for (DelayDspOrder order : list) {

            DelayDspOrderDTO dto = new DelayDspOrderDTO();
            dto.setOrderId(order.getOrderId());
            dto.setIsDelay(order.getIsDelay());
            if (taskMap.containsKey(order.getTaskId())) {
                DelayDspTask task = taskMap.get(order.getTaskId());
                dto.setTaskId(task.getTaskId());
                dto.setExecuteTime(DateUtil.formatDate(task.getExecuteTime(), DateUtil.DATE_FMT));
                dto.setExecuteTimeDeadline(DateUtil.formatDate(task.getExecuteTimeDeadline(), DateUtil.DATE_FMT));
            }
            response.getData().add(dto);
        }
        return response;
    }
}
