package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.request.SaveAdjacencyRouteRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspTaskApplicationService;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.service.*;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspTaskApplicationServiceImpl implements DelayDspTaskApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskApplicationServiceImpl.class);

    @Autowired
    private DispatchService dispatchService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private DispatchConfigService dispatchConfigService;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DistributedCache distributedCache;

    @Autowired
    private SupplyOrderService supplyOrderService;

    @Autowired
    private DispatcherDriverService dispatcherDriverService;

    @Override
    public void dispatch(DispatchDelayDspTaskRequest request) {

        DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
        logger.info("DelayDispatchApplicationServiceImpl_dispatch", JsonUtil.toJson(task));
        if (Objects.isNull(task)) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "task is null! task id: {}", request.getTaskId());
            return;
        }
        if (!Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.EXECUTING.getCode())) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "task is complete! task id: {}", request.getTaskId());
            return;
        }
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
        if (CollectionUtils.isEmpty(orders)) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "order is empty! task id: {}", request.getTaskId());
            return;
        }
        long start = System.currentTimeMillis();
        DispatchResult result = new DispatchResult(orders);
        List<DriverAggregation> driverAggregations = Lists.newArrayList();
        try {
            DispatcherConfig config = dispatchConfigService.buildDispatcherConfig(task);

            driverAggregations = driverService.queryDriverAggregation(config.getDriverProfitDayBaselineValue(), task);
            // 司机开始匹配
            dispatcherDriverService.join(task, driverAggregations);
            // 派单
            result = dispatchService.dispatch(new DelayDspContext(task, orders, driverAggregations, config));
            logger.info("DelayDspTaskApplicationServiceImpl_dispatch", JsonUtil.toJson(result));
            // 更新任务状态
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        } catch (Exception e) {
            MetricsUtil.recordValue("dispatch.task.error", 1);
            logger.error("DelayDspTaskApplicationServiceImpl.dispatch.error", e);
            // 派发异常，将全部订单都改派出去
        } finally {
            long end = System.currentTimeMillis();
            MetricsUtil.recordTime("dispatch.total.time", end - start);
            // 保存派单结果
            saveDispatchResult(task, result.getDetails());
            // 延后派订单出池
            supplyOrderService.batchFrozen(result.getMatchDetails());
            // 司机结束匹配
            dispatcherDriverService.exit(driverAggregations);
            MetricsUtil.recordValue("order.match.total", result.getMatchDetails().size() + result.getUnMatchDetails().size());
        }
    }

    @Override
    public void saveAdjacencyEdge(SaveAdjacencyRouteRequest request) {
        try {
            DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
            if (Objects.isNull(task) || !task.isUnExecuted()) {
                logger.info("DelayDispatchApplicationServiceImpl.saveAdjacencyRoute", "task has executed.order: {}, task:{}", request.getOrderId(), JsonUtil.toJson(task));
                return;
            }
            DelayDspOrder order = delayDspOrderRepository.queryByOrderId(request.getOrderId());
            if (Objects.isNull(order) || Objects.equals(order.getIsCancel(), YesOrNo.YES.getCode())) {
                logger.info("DelayDispatchApplicationServiceImpl.saveAdjacencyRoute", "order is cancel.order: {}, task:{}", request.getOrderId(), JsonUtil.toJson(task));
                return;
            }
            // 查询相同任务的邻接订单
            List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId());
            List<Driver> drivers = driverService.queryDriver(task);
            saveAdjacencyEdge(task, orders, order, drivers);
        } catch (Exception e) {
            MetricsUtil.recordValue("save.adjacency.route.error", 1);
            logger.error("save route error", e);
        }
    }

    public void saveAdjacencyEdge(DelayDspTask task, List<DelayDspOrder> adjacency, DelayDspOrder order, List<Driver> drivers) {
        if (Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            drivers = drivers.stream()
                    .filter(d -> Objects.equals(d.getDriverId(), order.getDriverId()))
                    .collect(Collectors.toList());
        }
        List<Position> positions = ModelFactory.buildPositions(task, order, adjacency, drivers);
        // 过滤已经缓存了的点位
        positions = filter(task, positions);
        if (CollectionUtils.isEmpty(positions)) {
            return;
        }
        List<Route> routes = geoGateway.queryRoutes(task.getCityId(), positions, order.getSysExpectBookTime(), order.getOrderId());
        cacheEdges(task, routes);
    }

    private void cacheEdges(DelayDspTask task, List<Route> routes) {
        String[] kv = new String[routes.size() * 2];
        for (int i = 0; i < routes.size(); i++) {
            Route route = routes.get(i);
            int j = 2 * i;
            kv[j] =  Route.toKey(task.getTaskId(), route.getHash());
            kv[j + 1] = Route.toValue(route.getDistance(), route.getDuration());
        }
        int expire = (int) DateUtil.secondsDiff(new Date(), task.getExecuteTimeDeadline()) + new Random().nextInt(3600);
        distributedCache.mset(expire, kv);
    }

    private List<Position> filter(DelayDspTask task, List<Position> positions) {
        if (CollectionUtils.isEmpty(positions)) {
            return Lists.newArrayList();
        }
        List<Position> result = Lists.newArrayList();

        List<String> key = positions.stream()
                .map(p -> Route.toKey(task.getTaskId(), p.hash()))
                .collect(Collectors.toList());

        List<String> values = distributedCache.mget(key);

        for (int i = 0; i < values.size(); i++) {
            String v = values.get(i);
            if (StringUtils.isBlank(v)) {
                result.add(positions.get(i));
            }
        }
        return result;
    }

    private void saveDispatchResult(DelayDspTask task, List<DispatchResultDetail> details) {
        try {
            List<DelayDspTaskRecord> records = ModelFactory.buildDelayDspTaskRecords(task, details);
            delayDspTaskRepository.save(records);
        } catch (Exception e) {
            logger.error(e);
        }
    }

}
