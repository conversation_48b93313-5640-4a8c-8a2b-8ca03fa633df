package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.service.DispatchTaskApplicationService;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.*;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("dispatchTaskApplicationServiceImpl")
public class DispatchTaskApplicationServiceImpl implements DispatchTaskApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(DispatchTaskApplicationServiceImpl.class);

    @Autowired
    private DriverService driverService;

    @Autowired
    private DispatchConfigService dispatchConfigService;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DistributedCache distributedCache;

    @Autowired
    private SupplyOrderService supplyOrderService;

    @Autowired
    private DelayDspContextFactory delayDspContextFactory;

    @Autowired
    private SelfDriverOrderGateway driverOrderGateway;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private DispatcherDriverService dispatcherDriverService;

    @Override
    public void dispatch(DispatchDelayDspTaskRequest request) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
        if (Objects.isNull(task)) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "task is null! task id: {}", request.getTaskId());
            return;
        }
        if (!Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.EXECUTING.getCode())) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "task is complete! task id: {}", request.getTaskId());
            return;
        }
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
        if (CollectionUtils.isEmpty(orders)) {
            logger.info("DelayDispatchApplicationServiceImpl.dispatch", "order is empty! task id: {}", request.getTaskId());
            return;
        }
        long start = System.currentTimeMillis();
        List<DriverAggregation> driverAggregations = Lists.newArrayList();
        DispatchResult result = new DispatchResult(orders);
        try {
            DelayDspContext delayDspContext = delayDspContextFactory.createDelayDspContext(task, orders);
            // 司机开始匹配
            driverAggregations = delayDspContext.getDriverAggregations();
            dispatcherDriverService.join(delayDspContext.getDelayDspTask(), delayDspContext.getDriverAggregations());

            DispatchTaskService dispatchTaskService = DispatcherManager.get(DelayDspTaskType.of(task.getTaskType()));
            // 派单
            result = dispatchTaskService.dispatch(delayDspContext);
            logger.info("DispatchTaskApplicationServiceImpl_dispatch", JsonUtil.toJson(result));
            // 创建司机单
            batchCreateDriverOrder(result.getMatchDetails());
            // 延后派订单出池
            supplyOrderService.batchFrozen(result.getMatchDetails());
            // 更新任务状态
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        } catch (Exception e) {
            MetricsUtil.recordValue("dispatch.task.error", 1);
            logger.error("DelayDspTaskApplicationServiceImpl.dispatch.error", e);
            // 派发异常，将全部订单都改派出去
        } finally {
            long end = System.currentTimeMillis();
            MetricsUtil.recordTime("dispatch.total.time", end - start);
            // 保存派单结果
            saveDispatchResult(task, result.getDetails());
            // 司机结束匹配
            dispatcherDriverService.exit(driverAggregations);
            MetricsUtil.recordValue("order.match.total", result.getMatchDetails().size() + result.getUnMatchDetails().size());
        }
    }

    protected void batchCreateDriverOrder(List<DispatchResultDetail> details) {
        for (DispatchResultDetail detail : details) {
            try {
                if (Objects.equals(detail.getMatchSuccess(), YesOrNo.NO.getCode()) || Objects.equals(detail.getOrder().getIsDelay(), YesOrNo.NO.getCode())) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(detail.getOrder().getOrderSource(), OrderSource.QUNAR.name())) {
                    detail.setDriverOrderId(detail.getOrder().getOrderId());
                } else {
                    // 仅未接单并且匹配到司机的延后派订单，需要创建司机单
                    String driverOrderId = driverOrderGateway.create(detail.getOrder(), detail.getDriver());
                    detail.setDriverOrderId(driverOrderId);
                }
            } catch (Exception e) {
                MetricsUtil.recordValue("createDriverOrder.error", 1);
                logger.error("DelayDspTaskApplicationServiceImpl_batchCreateDriverOrder_error", e);
            }
        }
    }

    public void saveDispatchResult(DelayDspTask task, List<DispatchResultDetail> details) {
        try {
            List<DispatchResultDetail> list = details;
            List<DelayDspTaskRecord> oldRecords = delayDspTaskRepository.queryRecordByTaskId(task.getTaskId(), YesOrNo.NO.getCode());
            logger.info("DispatchTaskApplicationServiceImpl_oldDelayDspTaskRecord", JsonUtil.toJson(oldRecords));
            if (CollectionUtils.isNotEmpty(oldRecords)) {
                Set<String> orderIds = oldRecords.stream().map(DelayDspTaskRecord::getOrderId).collect(Collectors.toSet());
                list = details.stream().filter(detail -> !orderIds.contains(detail.getOrder().getOrderId())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(list)) {
                List<DelayDspTaskRecord> records = ModelFactory.buildDelayDspTaskRecords(task, list);
                logger.info("DispatchTaskApplicationServiceImpl_saveDispatchResult", JsonUtil.toJson(records));
                delayDspTaskRepository.save(records);
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }
}
