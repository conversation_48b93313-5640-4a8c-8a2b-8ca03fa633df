package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.IncreaseVirtualDspTakenFailCountRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderResponseType;

/**
 * <AUTHOR>
 */
public interface DelayDspOrderApplicationService {

    InsertDelayDspOrderResponseType insertDelayDspOrder(InsertDelayDspOrderRequestType request);

    void insertTakenDelayOrder(String orderId, String orderSource);

    QueryDelayDspOrderResponseType queryDelayDspOrder(QueryDelayDspOrderRequestType request);

    void updateDelayDspOrder(UpdateDelayDspOrderRequest request);

    void updateDelayDspOrderForCancel(UpdateDelayDspOrderRequest request);


    Long increaseVirtualDspTakenFailCount(IncreaseVirtualDspTakenFailCountRequestType request);

    QueryDelayDspOrderBatchResponseType queryDelayDspOrderBatch(QueryDelayDspOrderBatchRequestType request);
    
    void syncDelayDspOrder(String orderId, String driverId);
    
    Boolean checkSpecialDate(Integer cityId, Integer carTypeId, String sysExpectBookTime);

    void updateTaskInfo(DelayDspTask delayDspTask);

}
