package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.delay.ratelimit.RedisRateLimiter;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Redis限流器使用示例
 * 展示如何在业务场景中使用Redis分布式限流器
 * <AUTHOR>
 */
@Service
public class RedisRateLimiterExampleService {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterExampleService.class);

    @Autowired(required = false)
    private RedisRateLimiter redisRateLimiter;

    /**
     * 订单接单限流
     * 限制司机在单位时间内接单的数量
     */
    public boolean checkDriverTakeOrderLimit(Long driverId, String orderId) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return true;
        }

        String rateLimiterKey = "driver_take_order:" + driverId;
        
        try {
            boolean allowed = redisRateLimiter.tryAcquire(rateLimiterKey);
            
            if (allowed) {
                logger.info("Driver {} allowed to take order {}", driverId, orderId);
            } else {
                logger.warn("Driver {} rate limited for order {}, available tokens: {}", 
                           driverId, orderId, redisRateLimiter.getAvailableTokens(rateLimiterKey));
            }
            
            return allowed;
        } catch (Exception e) {
            logger.error("Rate limiter error for driver {}, order {}", driverId, orderId, e);
            return true; // 异常时默认通过
        }
    }

    /**
     * 订单派发限流
     * 限制每个城市的订单派发速率
     */
    public boolean checkCityDispatchLimit(Integer cityId, int orderCount) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return true;
        }

        String rateLimiterKey = "city_dispatch:" + cityId;
        
        try {
            // 批量获取令牌，带超时
            boolean allowed = redisRateLimiter.tryAcquire(rateLimiterKey, orderCount, 500);
            
            if (allowed) {
                logger.info("City {} allowed to dispatch {} orders", cityId, orderCount);
            } else {
                long available = redisRateLimiter.getAvailableTokens(rateLimiterKey);
                logger.warn("City {} rate limited for {} orders, only {} tokens available", 
                           cityId, orderCount, available);
            }
            
            return allowed;
        } catch (Exception e) {
            logger.error("Rate limiter error for city {}, orderCount {}", cityId, orderCount, e);
            return true;
        }
    }

    /**
     * API接口限流
     * 限制用户调用API的频率
     */
    public boolean checkApiRateLimit(String userId, String apiPath) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return true;
        }

        String rateLimiterKey = String.format("api:%s:%s", apiPath, userId);
        
        try {
            boolean allowed = redisRateLimiter.tryAcquire(rateLimiterKey);
            
            if (!allowed) {
                logger.warn("User {} rate limited for API {}", userId, apiPath);
            }
            
            return allowed;
        } catch (Exception e) {
            logger.error("Rate limiter error for user {}, API {}", userId, apiPath, e);
            return true;
        }
    }

    /**
     * 全局限流
     * 对整个系统的某个功能进行限流
     */
    public boolean checkGlobalRateLimit(String feature) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return true;
        }

        String rateLimiterKey = "global:" + feature;
        
        try {
            return redisRateLimiter.tryAcquire(rateLimiterKey);
        } catch (Exception e) {
            logger.error("Global rate limiter error for feature {}", feature, e);
            return true;
        }
    }

    /**
     * 查询限流器状态
     * 获取某个限流器的剩余令牌数
     */
    public long getRateLimiterStatus(String key) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return -1;
        }

        try {
            return redisRateLimiter.getAvailableTokens(key);
        } catch (Exception e) {
            logger.error("Error getting rate limiter status for key {}", key, e);
            return -1;
        }
    }

    /**
     * 重置限流器
     * 清除某个限流器的状态
     */
    public void resetRateLimiter(String key) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return;
        }

        try {
            redisRateLimiter.reset(key);
            logger.info("Rate limiter reset for key: {}", key);
        } catch (Exception e) {
            logger.error("Error resetting rate limiter for key {}", key, e);
        }
    }

    /**
     * 批量操作限流
     * 用于需要批量获取资源的场景
     */
    public boolean checkBatchOperationLimit(String operationType, int batchSize) {
        if (redisRateLimiter == null || !redisRateLimiter.isEnabled()) {
            return true;
        }

        String rateLimiterKey = "batch:" + operationType;
        
        try {
            // 根据批量大小获取相应数量的令牌
            boolean allowed = redisRateLimiter.tryAcquire(rateLimiterKey, batchSize, 1000);
            
            if (allowed) {
                logger.info("Batch operation {} with size {} allowed", operationType, batchSize);
            } else {
                logger.warn("Batch operation {} with size {} rate limited", operationType, batchSize);
            }
            
            return allowed;
        } catch (Exception e) {
            logger.error("Rate limiter error for batch operation {}, size {}", operationType, batchSize, e);
            return true;
        }
    }
}
