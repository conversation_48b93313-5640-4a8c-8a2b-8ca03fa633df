package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.application.util.ResponseUtil;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.*;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.gateway.SupplyOrderGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.IncreaseVirtualDspTakenFailCountRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.dcs.dsp.delay.validator.impl.VirtualDspValidator;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspOrderApplicationServiceImpl implements DelayDspOrderApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderApplicationServiceImpl.class);

    @Autowired
    private DispatchConfigService dispatchConfigService;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private SupplyOrderGateway supplyOrderGateway;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DistributedCache distributedCache;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private ValidatorChain validatorChain;

    @Autowired
    private DelayTaskService delayTaskService;

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;
    @Resource
    DelayDspOrderService delayDspOrderService;

    @Resource
    SpecialDateConfig specialDateConfig;

    @Override
    public InsertDelayDspOrderResponseType insertDelayDspOrder(InsertDelayDspOrderRequestType request) {
        String driverId = null;
        ValidatorDTO code = null;
        SupplyOrder supplyOrder = null;
        //开始记时
        long startTime = System.currentTimeMillis();
        try {
            supplyOrder = supplyOrderGateway.query(request.getOrderId());
            if (Objects.isNull(supplyOrder) || supplyOrder.isCancel()) {
                return ResponseUtil.buildInsertDelayDspOrderResponse(request.getOrderId());
            }
            // 订单延后派入池校验
            code = validatorChain.validate(supplyOrder);
            if (!ValidatorCode.OK.equals(code.getCode())) {
                logger.info("DelayDspOrderApplicationServiceImpl_insertDelayDspOrder", "order: {}, Validator Code: {}", supplyOrder.getOrderId(), code.getCode().name());
                MetricsUtil.recordValue("insert.delay.dsp.order.validator." + code.getCode().name(), 1);
                return ResponseUtil.buildInsertDelayDspOrderResponse(supplyOrder.getOrderId());
            }
            DelayDspTask task = delayTaskService.queryOrCreate(supplyOrder);
            logger.info("DelayDspOrderApplicationServiceImpl_task", "task: {}", JsonUtil.toJson(task));
            if (Objects.isNull(task) || !task.isUnExecuted()) {
                return ResponseUtil.buildInsertDelayDspOrderResponse(supplyOrder.getOrderId());
            }
            if (task.getExecuteTime().after(supplyOrder.getSysExpectBookTime())) {
                MetricsUtil.recordValue("insert.delay.dsp.order.afterBookTime", 1);
                return ResponseUtil.buildInsertDelayDspOrderResponse(supplyOrder.getOrderId());
            }
            // 查询延后派运力子产品
            DelayDspSubSkuValue subSku = dispatchConfigService.matchDelayDspSubSkuConfig(supplyOrder);
            // 构建延后派订单
            DelayDspOrder order = ModelFactory.buildDelayDspOrder(supplyOrder, task, subSku);
            if(code.getDriverAggregations() != null){
                driverId = code.getDriverAggregations().getDriver().getDriverId();
            }
            delayDspOrderRepository.save(order);
            // 发送延后派订单入池消息
            sendMessage(order, task);
            MetricsUtil.recordValue("insert.delay.dsp.order.success", 1);
            return ResponseUtil.buildInsertDelayDspOrderResponse(
                    order.getOrderId(),
                    order.getIsDelay(),
                    task.getExecuteTime(),
                    task.getExecuteTimeDeadline(),
                    delayDspCommonQConfig.getVirtualDspSwitch()
            );
        } catch (Exception e) {
            MetricsUtil.recordValue("insert.delay.dsp.order.error", 1);
            logger.error("insertDelayDspOrder error!", e);
            if(StringUtils.isNotBlank(driverId)){
                //发送消息释放虚拟库存
                Map<String, Object> map = Maps.newHashMap();
                map.put("dspOrderId", request.getOrderId());
                map.put("driverId", driverId);
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_RELEASE_INVENTORY_FOR_INSERT_SUBJECT, map);
            }
        } finally {
            if (Objects.nonNull(code)) {
                //记录延后派入池原因
                delayDspOrderService.recordInsertDspPoolReasonRecord(code, supplyOrder, startTime, System.currentTimeMillis());
            }
        }
        return ResponseUtil.buildInsertDelayDspOrderResponse(request.getOrderId());
    }
    
    @Override
    public void insertTakenDelayOrder(String orderId, String orderSource) {
        logger.info("DelayDspOrderApplicationServiceImpl_insertTakenDelayOrder", "orderId: {}, orderSource: {}", orderId, orderSource);
        SupplyOrder supplyOrder = null;
        if (StringUtils.equalsIgnoreCase(orderSource,OrderSource.QUNAR.name())) {
            supplyOrder = supplyOrderGateway.query(orderId);
        }
        if (StringUtils.equalsIgnoreCase(orderSource,OrderSource.CTRIP.name())) {
            supplyOrder = selfDispatcherOrderGateway.query(orderId);
        }
        if (Objects.isNull(supplyOrder) || !supplyOrder.isTaken() || CategoryCode.isDayRental(supplyOrder.getCategoryCode())) {
            return;
        }
        // 更新订单信息
        DelayDspOrder tempOrder = ModelFactory.buildDelayDspOrder(supplyOrder);
        delayDspOrderRepository.update(tempOrder);
        // 订单延后派入池校验
        ValidatorDTO code = validatorChain.validate(supplyOrder);
        if (!ValidatorCode.OK.equals(code.getCode())) {
            return;
        }
        Driver driver = driverGateway.query(supplyOrder.getDriverId());
        if (Objects.isNull(driver)) {
            return;
        }
        List<DelayDspTask> tasks = delayDspTaskRepository.query(driver.getCityId(), driver.getCarTypeId(), supplyOrder.getSysExpectBookTime());
        logger.info("DelayDspOrderApplicationServiceImpl_insertTakenDelayOrder", JsonUtil.toJson(tasks));
        if (CollectionUtils.isEmpty(tasks)) {
            DelayDspTask task = delayTaskService.queryOrCreate(supplyOrder);
            if (Objects.nonNull(task)) {
                tasks = Lists.newArrayList(task);
            }
        }
        // 查询延后派运力子产品
        DelayDspSubSkuValue subSku = dispatchConfigService.matchDelayDspSubSkuConfig(supplyOrder);
        for (DelayDspTask task : tasks) {
            OrderSource orderSourceEnum = StringUtils.equalsIgnoreCase(orderSource, OrderSource.QUNAR.name()) ? OrderSource.QUNAR : OrderSource.CTRIP;
            DelayDspOrder order = ModelFactory.buildDelayDspOrder(supplyOrder, task, subSku, orderSourceEnum);
            delayDspOrderRepository.save(order);
            // 发送延后派订单入池消息
            sendMessage(order, task);
        }
    }
    
    @Override
    public void syncDelayDspOrder(String orderId, String driverId) {
        ValidatorDTO code = null;
        try {
            SupplyOrder supplyOrder = supplyOrderGateway.query(orderId);
            if (supplyOrder == null) {
                return;
            }
            //将driverId、同步标识 赋值给订单
            supplyOrder.setDriverId(driverId);
            supplyOrder.setSyncKmOrder(Boolean.TRUE);
            // 订单延后派入池校验
            code = validatorChain.validate(supplyOrder);
            if (!ValidatorCode.OK.equals(code.getCode())) {
                return;
            }
            Driver driver = driverGateway.query(driverId);
            if (Objects.isNull(driver)) {
                return;
            }
            List<DelayDspTask> tasks = delayDspTaskRepository.query(driver.getCityId(), driver.getCarTypeId(), supplyOrder.getSysExpectBookTime());
            if (CollectionUtils.isEmpty(tasks)) {
                DelayDspTask task = delayTaskService.queryOrCreate(driver, supplyOrder.getSysExpectBookTime(),supplyOrder.getShortDisOrder());
                if (Objects.nonNull(task)) {
                    tasks = Lists.newArrayList(task);
                }
            }
            for (DelayDspTask task : tasks) {
                DelayDspOrder order = ModelFactory.buildOldDelayDspOrder(supplyOrder, task);
                delayDspOrderRepository.save(order);
                // 发送延后派订单入池消息
                sendMessage(order, task);
            }
        } finally {
            if (code != null && code.getCode() != null) {
                MetricsUtil.recordValueWithTag("sync.km.order.result",1, "code", code.getCode().name());
            }
        }
    }
    
    @Override
    public Boolean checkSpecialDate(Integer cityId, Integer carTypeId, String sysExpectBookTime) {
        return delayDspOrderService.checkSpecialDate(cityId, carTypeId, sysExpectBookTime);
    }

    @Override
    public void updateTaskInfo(DelayDspTask delayDspTask) {
        delayTaskService.updateTaskInfo(delayDspTask);
    }

    @Override
    public void updateDelayDspOrder(UpdateDelayDspOrderRequest request) {
        DelayDspOrder delayDspOrder = delayDspOrderRepository.queryByOrderId(request.getOrderId());
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrder_queryByOrderId", JsonUtil.toJson(delayDspOrder));
        if (Objects.isNull(delayDspOrder)) {
            return;
        }
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(delayDspOrder.getTaskId());
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrder_queryByTaskId", JsonUtil.toJson(task));
        if (Objects.isNull(task) || !task.isUnExecuted()) {
            return;
        }
        SupplyOrder supplyOrder = null;
        if (StringUtils.equalsIgnoreCase(delayDspOrder.getOrderSource(),OrderSource.QUNAR.name())) {
            supplyOrder = supplyOrderGateway.query(request.getOrderId());
        }
        if (StringUtils.equalsIgnoreCase(delayDspOrder.getOrderSource(),OrderSource.CTRIP.name())) {
            supplyOrder = selfDispatcherOrderGateway.query(request.getOrderId());
        }
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrder_query", JsonUtil.toJson(supplyOrder));
        if (Objects.isNull(supplyOrder)) {
            return;
        }
        DelayDspOrder order = ModelFactory.buildDelayDspOrder(supplyOrder);
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrder", "order info: {}", JsonUtil.toJson(order));
        delayDspOrderRepository.update(order);
    }


    @Override
    public void updateDelayDspOrderForCancel(UpdateDelayDspOrderRequest request) {
        DelayDspOrder delayDspOrder = delayDspOrderRepository.queryByOrderId(request.getOrderId());
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrderForCancel_queryByOrderId", JsonUtil.toJson(delayDspOrder));
        if (Objects.isNull(delayDspOrder)) {
            return;
        }
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(delayDspOrder.getTaskId());
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrderForCancel_queryByTaskId", JsonUtil.toJson(task));
        if (Objects.isNull(task)) {
            return;
        }
        SupplyOrder supplyOrder = null;
        if (StringUtils.equalsIgnoreCase(delayDspOrder.getOrderSource(),OrderSource.QUNAR.name())) {
            supplyOrder = supplyOrderGateway.query(request.getOrderId());
        }
        if (StringUtils.equalsIgnoreCase(delayDspOrder.getOrderSource(),OrderSource.CTRIP.name())) {
            supplyOrder = selfDispatcherOrderGateway.query(request.getOrderId());
        }
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrderForCancel_query", JsonUtil.toJson(supplyOrder));
        if (Objects.isNull(supplyOrder)) {
            return;
        }
        DelayDspOrder order = ModelFactory.buildDelayDspOrder(supplyOrder);
        logger.info("DelayDspOrderApplicationServiceImpl_updateDelayDspOrderForCancel", "order info: {}", JsonUtil.toJson(order));
        delayDspOrderRepository.updateForCancel(order);
    }



    @Override
    public Long increaseVirtualDspTakenFailCount(IncreaseVirtualDspTakenFailCountRequestType request) {
        if (!Objects.equals(delayDspCommonQConfig.getVirtualDspSwitch(), YesOrNo.YES.getCode())) {
            logger.info("DelayDspOrderApplicationServiceImpl.increaseVirtualDspTakenFailCount", "virtual dsp switch close. order id: {}", request.getOrderId());
            return NumberUtils.LONG_ZERO;
        }
        DelayDspOrder order = delayDspOrderRepository.queryByOrderId(request.getOrderId());
        if (Objects.isNull(order) || Objects.equals(order.getIsCancel(), YesOrNo.YES.getCode())) {
            logger.info("DelayDspOrderApplicationServiceImpl.increaseVirtualDspTakenFailCount", "order not in db. order id: {}", request.getOrderId());
            return NumberUtils.LONG_ZERO;
        }
        String key = VirtualDspValidator.toKey(order.getCityId(), order.getCarTypeId(), order.getSysExpectBookTime());
        Date date = DateUtil.addHours(order.getSysExpectBookTime(), 1);
        long expire = (long) DateUtil.secondsDiff(new Date(), date);
        // 加入集合
        distributedCache.sadd(key, expire, request.getOrderId());
        // 集合中元素的数量
        return distributedCache.scard(key);
    }

    @Override
    public QueryDelayDspOrderBatchResponseType queryDelayDspOrderBatch(QueryDelayDspOrderBatchRequestType request) {
        if (CollectionUtils.isEmpty(request.getOrderIds())) {
            return ResponseUtil.buildQueryDelayDspOrderBatchResponse();
        }
        List<DelayDspOrder> list = delayDspOrderRepository.queryByOrderIds(request.getOrderIds());
        if (CollectionUtils.isEmpty(list)) {
            return ResponseUtil.buildQueryDelayDspOrderBatchResponse();
        }
        Set<Long> taskIds = list.stream().map(DelayDspOrder::getTaskId).collect(Collectors.toSet());
        List<DelayDspTask> tasks = delayDspTaskRepository.queryByTaskIds(taskIds);
        return ResponseUtil.buildQueryDelayDspOrderBatchResponse(list, tasks);
    }

    @Override
    public QueryDelayDspOrderResponseType queryDelayDspOrder(QueryDelayDspOrderRequestType request) {
        DelayDspOrder order = null;
        if (StringUtils.isNotBlank(request.getOrderId())) {
            order = delayDspOrderRepository.queryByOrderId(request.getOrderId());
        } else if (StringUtils.isNotBlank(request.getMainOrderId())) {
            order = delayDspOrderRepository.queryByMainOrderId(request.getMainOrderId());
        }
        if (order == null) {
            return ResponseUtil.buildQueryDelayDspOrderResponse();
        }
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(order.getTaskId());
        if (task == null) {
            return ResponseUtil.buildQueryDelayDspOrderResponse(order.getOrderId());
        }
        return ResponseUtil.buildQueryDelayDspOrderResponse(order.getOrderId(), order.getIsDelay(), task.getTaskId(), task.getExecuteTime(), task.getExecuteTimeDeadline());
    }

    public long sendMessage(DelayDspOrder order, DelayDspTask task) {
        try {
            HashMap<String, Object> msg = Maps.newHashMap();
            msg.put("orderId", order.getOrderId());
            msg.put("orderSource", order.getOrderSource());
            msg.put("taskId", task.getTaskId());
            msg.put("taskType", task.getTaskType());
            if (Objects.equals(order.getIsDelay(), YesOrNo.YES.getCode())) {
                msg.put("duid", order.getDuid());
            }
            cacheEdgesForMatchTask(task, msg);
            Date now = new Date();
            Date date = DateUtil.addHours(task.getExecuteTime(), -delayDspCommonQConfig.getBeforeExecuteTimeHour());
            if (date.before(now)) {
                // 当前时间距离任务执行时间在配置阈值范围内，立即处理
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg);
                return 0L;
            } else {
                // 延迟处理
                long delay = date.getTime() - now.getTime() + new Random().nextInt(60 * 60) * 1000;
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg, delay);
                return delay;
            }
        } catch (Exception e) {
            logger.error("DelayDspOrderApplicationServiceImpl_sendMessage_error", e);
            return -1L;
        }
    }

    private void cacheEdgesForMatchTask(DelayDspTask task, HashMap<String, Object> msg) {
        try{
            Date now = new Date();
            Date date = DateUtil.addHours(task.getMatchTime(), -delayDspCommonQConfig.getBeforeExecuteTimeHour());
            if (date.before(now)) {
                // 当前时间距离任务执行时间在配置阈值范围内，立即处理
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg);
            } else {
                // 延迟处理
                long delay = date.getTime() - now.getTime() + new Random().nextInt(60 * 60) * 1000;
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg, delay);
            }
        }catch (Exception ex){
            logger.error("DelayDspOrderApplicationServiceImpl_cacheEdgesForMatchTask_error", ex);
        }
    }

}
